# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# JavaScript, TypeScript, Svelte files
[*.{js,ts,svelte}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.{json,jsonc}]
indent_style = space
indent_size = 2

# CSS, SCSS files
[*.{css,scss}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false
max_line_length = 80

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Package.json
[package.json]
indent_style = space
indent_size = 2

# Environment files
[.env*]
indent_style = space
indent_size = 2

# Config files
[*.config.{js,mjs,cjs}]
indent_style = space
indent_size = 2

# Ignore files
[*.{ignore,gitignore}]
indent_style = space
indent_size = 2
