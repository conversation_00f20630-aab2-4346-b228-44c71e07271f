# Project Configuration
PROJECT_NAME=YourProjectName
PROJECT_VERSION=1.0.0
PROJECT_DESCRIPTION=Your project description

# Environment
NODE_ENV=development

# Database Configuration
DB_NAME=furpa.db
DB_PATH=./data

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=your_username
RABBITMQ_PASSWORD=your_password
RABBITMQ_VHOST=/
RABBITMQ_PROTOCOL=amqp
RABBITMQ_CONNECTION_TIMEOUT=10000
RABBITMQ_HEARTBEAT=60

# RabbitMQ Queues
RABBITMQ_QUEUE_USERS=users_queue
RABBITMQ_QUEUE_NOTIFICATIONS=notifications_queue
RABBITMQ_QUEUE_LOGS=logs_queue

# RabbitMQ Exchanges
RABBITMQ_EXCHANGE_MAIN=your_exchange
RABBITMQ_EXCHANGE_TYPE=topic

# Application Settings
APP_WINDOW_WIDTH=1200
APP_WINDOW_HEIGHT=800
APP_FULLSCREEN=true
APP_DEV_TOOLS=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
