module.exports = {
  // Temel Prettier Ayarları
  semi: false, // Noktalı virgül kullanma
  singleQuote: true, // Tek tırnak kullan
  quoteProps: 'as-needed', // Sadece gerektiğinde property'leri tırnak içine al
  trailingComma: 'es5', // ES5 uyumlu trailing comma
  tabWidth: 2, // Tab genişliği 2 space
  useTabs: false, // Space kullan, tab kullanma
  printWidth: 100, // Satır uzunluğu 100 karakter
  bracketSpacing: true, // Object literal'larda space kullan { foo: bar }
  bracketSameLine: false, // JSX closing bracket'ı yeni satırda
  arrowParens: 'avoid', // Arrow function'larda tek parametre için parantez kullanma
  endOfLine: 'lf', // Unix line endings

  // Svelte Özel Ayarları
  plugins: ['prettier-plugin-svelte'],
  overrides: [
    {
      files: '*.svelte',
      options: {
        parser: 'svelte',
        svelteStrictMode: false,
        svelteAllowShorthand: true,
        svelteIndentScriptAndStyle: true,
      },
    },
    {
      files: '*.js',
      options: {
        parser: 'babel',
      },
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none',
      },
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        printWidth: 80,
        proseWrap: 'preserve',
      },
    },
    {
      files: '*.css',
      options: {
        parser: 'css',
      },
    },
    {
      files: '*.html',
      options: {
        parser: 'html',
        printWidth: 120,
      },
    },
  ],
}
