# SevlteFurpa - Electron.js Desktop Application

A modern desktop application built with Electron.js, Svelte, Vite, and Bulma CSS framework.

## Technology Stack

- **Electron.js** - Desktop application framework
- **Svelte** - Frontend framework
- **Vite** - Build tool and development server
- **Bulma CSS** - CSS framework for styling
- **better-sqlite3** - Local SQLite database
- **svelte-french-toast** - Toast notifications
- **svelte-spa-router** - Client-side routing
- **RabbitMQ (amqplib)** - Message queue system
- **dotenv** - Environment variable management
- **Yarn** - Package manager

## Prerequisites

- Node.js (version 16 or higher)
- Yarn package manager
- RabbitMQ server (optional - application will work without it)

### RabbitMQ Installation

**Ubuntu/Debian:**

```bash
sudo apt-get install rabbitmq-server
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
```

**macOS (with Homebrew):**

```bash
brew install rabbitmq
brew services start rabbitmq
```

**Windows:**
Download and install from [RabbitMQ official website](https://www.rabbitmq.com/download.html)

**Docker:**

```bash
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
```

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd sevltefurpa
```

2. Install dependencies using Yarn:

```bash
yarn install
```

This will automatically:

- Install all dependencies
- Run `electron-rebuild` to rebuild native modules for Electron

3. Configure environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your specific configuration:

```env
# Project Configuration
PROJECT_NAME=YourProjectName
PROJECT_VERSION=1.0.0

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Application Settings
APP_WINDOW_WIDTH=1200
APP_WINDOW_HEIGHT=800
APP_FULLSCREEN=true
APP_DEV_TOOLS=true
```

## Development

### Start Development Server

To run the application in development mode:

```bash
yarn electron:dev
```

This command will:

- Start the Vite development server on `http://localhost:5173`
- Launch the Electron application
- Enable hot reload for the renderer process
- Open developer tools automatically

### Individual Commands

- **Start Vite dev server only:**

  ```bash
  yarn dev
  ```

- **Start Electron only:**

  ```bash
  yarn electron
  ```

- **Build for production:**

  ```bash
  yarn build
  ```

- **Preview production build:**

  ```bash
  yarn preview
  ```

- **Code formatting with Prettier:**

  ```bash
  yarn format              # Format all files
  yarn format:check        # Check formatting without changing files
  yarn format:svelte       # Format only Svelte files
  yarn format:js           # Format only JavaScript files
  ```

- **Code linting with ESLint:**

  ```bash
  yarn lint                # Lint all files
  yarn lint:fix            # Lint and auto-fix issues
  yarn lint:check          # Lint with zero warnings allowed (CI/CD)
  yarn lint:svelte         # Lint only Svelte files
  yarn lint:js             # Lint only JavaScript files
  yarn check               # Run both format:check and lint:check
  ```

## Building for Distribution

To create a distributable package:

```bash
yarn dist
```

This will:

1. Build the application using Vite
2. Package it using electron-builder

## Project Structure

```
sevltefurpa/
├── src/
│   ├── main/           # Electron main process
│   │   ├── main.js     # Main process entry point
│   │   └── database.js # SQLite database operations
│   └── renderer/       # Svelte frontend
│       ├── main.js     # Renderer entry point
│       ├── App.svelte  # Main app component
│       ├── index.html  # HTML template
│       └── pages/      # Application pages
│           ├── Home.svelte
│           └── Test.svelte
├── package.json        # Project configuration
├── yarn.lock          # Yarn lockfile
├── vite.config.mjs    # Vite configuration
└── svelte.config.js   # Svelte configuration
```

## Features

### Home Page

- User management system with CRUD operations
- SQLite database integration
- Real-time data updates
- Form validation and error handling

### Test Page

- **IPC communication testing** - Verify Electron IPC functionality
- **Database connection testing** - Test SQLite database operations
- **Toast notification testing** - Test notification system
- **Navigation + Toast testing** - Comprehensive routing reliability tests
- **RabbitMQ connection testing** - Message queue system tests
- **System information display** - Environment and configuration details

#### Navigation Fix

The application includes a comprehensive fix for navigation issues that could occur after toast notifications:

- **Toast Queue Management**: Prevents toast conflicts with routing
- **Navigation State Management**: Ensures reliable page transitions
- **Event Handling Isolation**: Separates toast and routing event systems
- **Comprehensive Testing**: Automated tests verify navigation works with various toast scenarios

### Technical Features

- **IPC Communication**: Secure communication between main and renderer processes
- **Database**: Local SQLite database with better-sqlite3
- **Routing**: Client-side routing with svelte-spa-router
- **Notifications**: Toast notifications with svelte-french-toast
- **Styling**: Modern UI with Bulma CSS framework
- **Background Image**: Customizable background image support with overlay effects
- **Hot Reload**: Development server with hot module replacement

## Package Management

This project uses **Yarn** as the package manager. Here are the common commands:

### Installing Dependencies

```bash
yarn install          # Install all dependencies
yarn add <package>    # Add a new dependency
yarn add -D <package> # Add a development dependency
```

### Removing Dependencies

```bash
yarn remove <package> # Remove a dependency
```

### Updating Dependencies

```bash
yarn upgrade          # Update all dependencies
yarn upgrade <package> # Update specific package
```

### Scripts

```bash
yarn <script-name>    # Run any script defined in package.json
```

## Configuration

### Background Image

The application supports a customizable background image that covers the entire application window:

#### Setup

1. **Add your background image**:

   - Place your image file as `bg.png` in `src/renderer/public/`
   - Supported formats: PNG, JPG, JPEG, WebP, SVG
   - Recommended: PNG with optimized file size (under 2MB for best performance)

2. **File location**:

   ```
   src/renderer/public/bg.png
   ```

   **Note**: The application uses progressive loading for optimal PNG performance.

#### Features

- **Full coverage**: Background covers the entire application window
- **Responsive scaling**: Uses `background-size: cover` for proper sizing
- **Progressive loading**: Preloads images for smooth performance
- **Overlay support**: Semi-transparent overlay for better text readability
- **Optimized blur effects**: Reduced blur intensity for better PNG performance
- **Fallback styling**: Graceful fallback if image fails to load
- **Performance optimizations**: Hardware acceleration and loading timeouts

#### Customization

To customize the background image behavior, edit the CSS in `src/renderer/App.svelte`:

```css
.app-container {
  background-image: url('/bg.png'); /* Set via JavaScript for progressive loading */
  background-size: cover; /* or contain, 100% 100%, auto */
  background-position: center; /* or top, bottom, left, right */
  background-attachment: fixed; /* or scroll */
}
```

For detailed customization options, see `BACKGROUND_IMAGE_SETUP.md`.

### Code Formatting (Prettier)

This project uses **Prettier** for consistent code formatting across all files:

#### Configuration Files

- **`.prettierrc.cjs`** - Main Prettier configuration
- **`.prettierignore`** - Files to exclude from formatting
- **`.editorconfig`** - Editor configuration for consistent coding styles
- **`.vscode/settings.json`** - VS Code specific settings

#### Prettier Settings

```javascript
{
  semi: false,                    // No semicolons
  singleQuote: true,             // Use single quotes
  trailingComma: 'es5',          // ES5 compatible trailing commas
  tabWidth: 2,                   // 2 spaces for indentation
  printWidth: 100,               // Line length limit
  bracketSpacing: true,          // Spaces in object literals
  arrowParens: 'avoid',          // No parens for single arrow function params
}
```

#### Supported File Types

- **Svelte** files (`.svelte`) - With svelte-specific formatting
- **JavaScript** files (`.js`) - ES6+ formatting
- **JSON** files (`.json`) - Structured formatting
- **Markdown** files (`.md`) - Documentation formatting
- **CSS** files (`.css`) - Style formatting
- **HTML** files (`.html`) - Markup formatting

#### VS Code Integration

The project includes VS Code settings for:

- **Format on save** - Automatically format files when saving
- **Format on paste** - Format pasted code
- **Svelte language support** - Proper syntax highlighting and formatting
- **Recommended extensions** - Auto-suggest useful extensions

#### Usage

```bash
# Format all files in the project
yarn format

# Check if files are properly formatted (CI/CD)
yarn format:check

# Format only Svelte files
yarn format:svelte

# Format only JavaScript files
yarn format:js
```

### Code Linting (ESLint)

This project uses **ESLint** for code quality and consistency checks, configured to work with the security-disabled Electron setup:

#### Configuration Files

- **`eslint.config.js`** - Main ESLint configuration (ESLint 9+ flat config)
- **`.vscode/settings.json`** - VS Code ESLint integration

#### ESLint Settings

```javascript
{
  // Permissive rules for security-disabled Electron app
  'no-console': 'off',              // Allow console usage
  'no-alert': 'off',                // Allow alert/confirm/prompt
  'no-unused-vars': 'warn',         // Warn on unused variables
  'prefer-const': 'warn',           // Prefer const over let
  'no-var': 'error',                // No var declarations
  'require-await': 'off',           // Allow async without await
}
```

#### Environment-Specific Rules

- **Main Process** (`src/main/**`): Node.js environment with Electron APIs
- **Renderer Process** (`src/renderer/**`): Browser + Node.js environment (security disabled)
- **Svelte Components** (`**/*.svelte`): Browser environment with Svelte-specific rules

#### Supported Features

- **✅ Direct require() usage** - No warnings for Node.js API usage in renderer
- **✅ Global Node.js variables** - process, \_\_dirname, Buffer, etc.
- **✅ Browser APIs** - setTimeout, localStorage, navigator, etc.
- **✅ Electron APIs** - ipcRenderer, webFrame, remote (security disabled)
- **✅ Svelte syntax** - Component lifecycle, reactive statements
- **✅ Prettier integration** - No formatting conflicts

#### Usage

```bash
# Lint all files
yarn lint

# Lint and auto-fix issues
yarn lint:fix

# Lint with zero warnings (CI/CD)
yarn lint:check

# Lint specific file types
yarn lint:svelte
yarn lint:js

# Combined check (format + lint)
yarn check
```

### Electron Security

The application is configured with **all security restrictions disabled** for maximum development flexibility:

- Node integration: **Enabled**
- Context isolation: **Disabled**
- Web security: **Disabled**
- Insecure content: **Allowed**
- Direct require() access: **Available**

**Note**: All security features are intentionally disabled for development convenience and maximum IPC flexibility. This configuration allows direct access to Node.js APIs and Electron modules from the renderer process without restrictions.

Security settings can be configured via environment variables:

```env
SECURITY_NODE_INTEGRATION=true
SECURITY_CONTEXT_ISOLATION=false
SECURITY_WEB_SECURITY=false
SECURITY_ALLOW_INSECURE_CONTENT=true
```

### Database

The SQLite database is automatically created in the project's `data/` directory and includes:

- **Database file**: `data/furpa.db`
- **User management table** with full CRUD operations
- **Sample data** for testing (John Doe, Jane Smith, Bob Johnson)
- **Automatic schema creation** on first run
- **Environment-based configuration** via `.env` file

Database configuration:

```env
DB_NAME=furpa.db
DB_PATH=./data
```

The `data/` directory is automatically created if it doesn't exist and is excluded from version control via `.gitignore`.

## Troubleshooting

### Native Module Issues

If you encounter issues with native modules (like better-sqlite3), run:

```bash
yarn install
```

The postinstall script will automatically rebuild native modules for Electron.

### Development Server Issues

If the development server fails to start:

1. Check if port 5173 is available
2. Clear node_modules and reinstall:
   ```bash
   rm -rf node_modules yarn.lock
   yarn install
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
