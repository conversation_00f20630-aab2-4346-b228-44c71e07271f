import js from '@eslint/js'
import prettier from 'eslint-config-prettier'
import svelte from 'eslint-plugin-svelte'

export default [
  // Base JavaScript configuration
  js.configs.recommended,

  // Svelte configuration
  ...svelte.configs['flat/recommended'],

  // Prettier integration (disables conflicting rules)
  prettier,
  ...svelte.configs['flat/prettier'],

  // Global settings
  {
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        // Browser globals
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        setTimeout: 'readonly',
        setInterval: 'readonly',
        clearTimeout: 'readonly',
        clearInterval: 'readonly',
        confirm: 'readonly',
        alert: 'readonly',
        prompt: 'readonly',

        // Node.js globals (allowed due to disabled security)
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
      },
    },
    plugins: {},
    rules: {
      // Code quality rules
      'no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
      'no-console': 'off', // Allow console for debugging
      'no-debugger': 'warn',
      'no-alert': 'off', // Allow alert, confirm, prompt (Electron app)

      // Import/Export rules (basic)
      'no-duplicate-imports': 'error',

      // General JavaScript rules
      'prefer-const': 'warn',
      'no-var': 'error',
      'object-shorthand': 'warn',
      'prefer-arrow-callback': 'warn',
      'prefer-template': 'warn',

      // Async/await rules
      'no-async-promise-executor': 'warn',
      'require-await': 'off', // Allow async functions without await

      // Error prevention
      'no-self-compare': 'error',
      'no-unreachable-loop': 'error',
      'array-callback-return': 'warn',
    },
  },

  // Main process specific configuration (src/main/**)
  {
    files: ['src/main/**/*.js'],
    languageOptions: {
      globals: {
        // Node.js environment for main process
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        global: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',

        // Electron main process globals
        app: 'readonly',
        BrowserWindow: 'readonly',
        ipcMain: 'readonly',
        dialog: 'readonly',
        Menu: 'readonly',
        shell: 'readonly',
      },
    },
    rules: {
      // Main process specific rules
      'no-console': 'off', // Allow console in main process
    },
  },

  // Renderer process specific configuration (src/renderer/**)
  {
    files: ['src/renderer/**/*.js'],
    languageOptions: {
      globals: {
        // Browser environment
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        location: 'readonly',
        history: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        fetch: 'readonly',
        setTimeout: 'readonly',
        setInterval: 'readonly',
        clearTimeout: 'readonly',
        clearInterval: 'readonly',

        // Node.js globals (allowed due to disabled security)
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',

        // Electron renderer globals (with disabled security)
        ipcRenderer: 'readonly',
        webFrame: 'readonly',
        remote: 'readonly',
      },
    },
    rules: {
      // Renderer process specific rules
      'no-console': 'off', // Allow console in renderer

      // Browser-specific rules
      'no-restricted-globals': ['error', 'event', 'fdescribe'],
    },
  },

  // Svelte component specific configuration
  {
    files: ['**/*.svelte'],
    languageOptions: {
      globals: {
        // Browser environment for Svelte
        window: 'readonly',
        document: 'readonly',
        navigator: 'readonly',
        location: 'readonly',
        history: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        fetch: 'readonly',
        setTimeout: 'readonly',
        setInterval: 'readonly',
        clearTimeout: 'readonly',
        clearInterval: 'readonly',
        confirm: 'readonly',
        alert: 'readonly',
        prompt: 'readonly',

        // Node.js globals (allowed due to disabled security)
        process: 'readonly',
        Buffer: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',

        // Electron renderer globals (with disabled security)
        ipcRenderer: 'readonly',
        webFrame: 'readonly',
        remote: 'readonly',
      },
    },
    rules: {
      // Svelte specific rules
      'svelte/no-at-debug-tags': 'warn',
      'svelte/no-reactive-functions': 'off', // May not exist in current version
      'svelte/no-reactive-literals': 'off', // May not exist in current version
      'svelte/valid-compile': 'off', // May not exist in current version

      // Accessibility rules (basic ones that should exist)
      'svelte/a11y-alt-text': 'off', // May not exist in current version
      'svelte/a11y-anchor-has-content': 'off', // May not exist in current version
      'svelte/a11y-anchor-is-valid': 'off', // May not exist in current version
      'svelte/a11y-click-events-have-key-events': 'off', // May not exist in current version
      'svelte/a11y-img-redundant-alt': 'off', // May not exist in current version
      'svelte/a11y-label-has-associated-control': 'off', // May not exist in current version
      'svelte/a11y-mouse-events-have-key-events': 'off', // May not exist in current version
      'svelte/a11y-no-redundant-roles': 'off', // May not exist in current version
      'svelte/a11y-role-has-required-aria-props': 'off', // May not exist in current version

      // Style rules (compatible with Prettier)
      'svelte/html-quotes': 'off', // Handled by Prettier
      'svelte/indent': 'off', // Handled by Prettier
      'svelte/max-attributes-per-line': 'off', // Handled by Prettier
      'svelte/first-attribute-linebreak': 'off', // Handled by Prettier
      'svelte/html-closing-bracket-spacing': 'off', // Handled by Prettier

      // Allow Node.js APIs in Svelte components (security disabled)
      'no-console': 'off', // Allow console in Svelte components
    },
  },

  // Configuration files
  {
    files: ['*.config.js', '*.config.mjs', '*.config.cjs'],
    languageOptions: {
      globals: {
        process: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
        require: 'readonly',
        module: 'readonly',
        exports: 'readonly',
      },
    },
    rules: {
      // Config files specific rules
      'no-console': 'off', // Allow console in config files
    },
  },

  // Ignore patterns
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      'release/**',
      'data/**',
      '*.db',
      '*.sqlite',
      '*.sqlite3',
      '.env*',
      'coverage/**',
      '*.min.js',
      '*.bundle.js',
    ],
  },
]
