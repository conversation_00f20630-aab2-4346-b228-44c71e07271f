{"name": "sevltefurpa", "version": "1.0.0", "description": "Electron.js desktop application with Svelte, Vite, and Bulma", "type": "module", "main": "src/main/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "e": "concurrently \"yarn dev\" \"wait-on http://localhost:5173 && NODE_ENV=development electron .\"", "electron": "electron .", "electron:dev": "concurrently \"yarn dev\" \"wait-on http://localhost:5173 && NODE_ENV=development electron .\"", "dist": "yarn build && electron-builder --publish=never", "postinstall": "electron-builder install-app-deps", "format": "prettier --write .", "format:check": "prettier --check .", "format:svelte": "prettier --write \"src/**/*.svelte\"", "format:js": "prettier --write \"src/**/*.js\"", "format:staged": "prettier --write", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:check": "eslint . --max-warnings 0", "lint:svelte": "eslint \"src/**/*.svelte\"", "lint:js": "eslint \"src/**/*.js\"", "check": "yarn format:check && yarn lint:check"}, "keywords": ["electron", "svelte", "vite", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"@eslint/js": "^9.28.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@types/amqplib": "^0.10.7", "@vitejs/plugin-legacy": "^5.0.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-svelte": "^3.9.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.4.0", "svelte": "^4.2.8", "vite": "^5.0.0", "wait-on": "^7.2.0"}, "dependencies": {"amqplib": "^0.10.8", "better-sqlite3": "^11.10.0", "bulma": "^0.9.4", "dotenv": "^16.5.0", "pg": "^8.16.0", "simple-keyboard": "^3.8.57", "svelte-french-toast": "^1.2.0", "svelte-spa-router": "^4.0.1"}, "build": {"appId": "com.example.sevltefurpa", "productName": "SevlteFurpa", "directories": {"output": "release"}, "files": ["src/main/**/*", "dist/**/*", "node_modules/**/*", "package.json", ".env"], "extraFiles": [{"from": ".env", "to": ".env"}]}}