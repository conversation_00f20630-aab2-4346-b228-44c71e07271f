import { authenticateEmployee } from './database.js'

class AuthService {
  constructor() {
    this.isAuthenticated = false
    this.currentEmployee = null
  }

  async initialize() {
    try {
      console.log('🔐 Authentication service initialized')
    } catch (error) {
      console.error('❌ Error initializing auth service:', error)
    }
  }

  async login(credentials) {
    try {
      const { username: code, password } = credentials

      // Authenticate employee using database
      const employee = await authenticateEmployee(code, password)

      if (!employee) {
        return {
          success: false,
          message: '<PERSON>eç<PERSON>iz kullanıcı kodu veya şifre',
        }
      }

      // Successful login
      this.isAuthenticated = true
      this.currentEmployee = { ...employee }
      delete this.currentEmployee.password // Don't keep password in memory

      console.log(`🔐 Employee logged in: ${employee.code} (${employee.name} ${employee.surname})`)

      return {
        success: true,
        user: {
          id: employee.id,
          uuid: employee.uuid,
          code: employee.code,
          name: employee.name,
          surname: employee.surname,
          market_id: employee.market_id,
          market_name: employee.market_name,
        },
        message: '<PERSON><PERSON><PERSON> ba<PERSON>ıl<PERSON>',
      }
    } catch (error) {
      console.error('❌ Login error:', error)
      return {
        success: false,
        message: '<PERSON>iriş sırasında bir hata oluştu',
      }
    }
  }

  async logout() {
    try {
      const employeeCode = this.currentEmployee?.code

      this.isAuthenticated = false
      this.currentEmployee = null

      console.log(`🔐 Employee logged out: ${employeeCode}`)

      return {
        success: true,
        message: 'Çıkış başarılı',
      }
    } catch (error) {
      console.error('❌ Logout error:', error)
      return {
        success: false,
        message: 'Çıkış sırasında bir hata oluştu',
      }
    }
  }

  checkAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      user: this.currentEmployee,
    }
  }

  getCurrentUser() {
    return this.currentEmployee
  }

  isUserAuthenticated() {
    return this.isAuthenticated
  }

  hasRole(role) {
    // For now, we'll use a simple role system based on employee code
    // In the future, this can be enhanced with the roles table
    if (this.currentEmployee?.code === '001') return role === 'admin'
    return role === 'employee'
  }

  hasAnyRole(roles) {
    return roles.some(role => this.hasRole(role))
  }
}

// Create singleton instance
const authService = new AuthService()

export default authService
