/**
 * Application Control IPC Handlers
 * Handles application-level IPC communication
 */

import { app, ipcMain } from 'electron'
import rabbitmqService from '../rabbitmq.js'

export function appHandlers() {
  // Close application
  ipcMain.handle('app:close', async () => {
    try {
      console.log('🚪 IPC: Application close requested')

      // Disconnect from RabbitMQ before closing
      try {
        await rabbitmqService.disconnect()
        console.log('✅ RabbitMQ disconnected successfully')
      } catch (error) {
        console.error('⚠️ Error disconnecting RabbitMQ:', error)
      }

      // Close the application
      app.quit()
      return { success: true, message: 'Application closing...' }
    } catch (error) {
      console.error('❌ IPC Error - app:close:', error)
      throw error
    }
  })

  console.log('✅ Application IPC handlers registered')
}
