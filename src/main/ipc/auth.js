/**
 * Authentication IPC Handlers
 * Handles all authentication-related IPC communication
 */

import { ipcMain } from 'electron'
import authService from '../auth.js'

export function authHandlers() {
  // User login
  ipcMain.handle('auth:login', async (_, credentials) => {
    try {
      return await authService.login(credentials)
    } catch (error) {
      console.error('❌ IPC Error - auth:login:', error)
      throw error
    }
  })

  // User logout
  ipcMain.handle('auth:logout', async () => {
    try {
      return await authService.logout()
    } catch (error) {
      console.error('❌ IPC Error - auth:logout:', error)
      throw error
    }
  })

  // Check if user is authenticated
  ipcMain.handle('auth:isUserAuthenticated', async () => {
    try {
      return authService.isUserAuthenticated()
    } catch (error) {
      console.error('❌ IPC Error - auth:isUserAuthenticated:', error)
      throw error
    }
  })

  // Get current user
  ipcMain.handle('auth:getCurrentUser', async () => {
    try {
      return authService.getCurrentUser()
    } catch (error) {
      console.error('❌ IPC Error - auth:getCurrentUser:', error)
      throw error
    }
  })

  // Get remembered username
  ipcMain.handle('auth:getRememberedUsername', async () => {
    try {
      return authService.getRememberedUsername()
    } catch (error) {
      console.error('❌ IPC Error - auth:getRememberedUsername:', error)
      throw error
    }
  })

  console.log('✅ Authentication IPC handlers registered')
}
