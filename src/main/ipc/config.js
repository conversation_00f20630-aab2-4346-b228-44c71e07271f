/**
 * Configuration IPC Handlers
 * Handles all configuration-related IPC communication
 */

import { ipcMain } from 'electron'
import { APP_CONFIG, PROJECT_CONFIG } from '../config.js'

export function configHandlers() {
  // Get project configuration
  ipcMain.handle('config:getProjectConfig', async () => {
    try {
      return PROJECT_CONFIG
    } catch (error) {
      console.error('❌ IPC Error - config:getProjectConfig:', error)
      throw error
    }
  })

  // Get app configuration
  ipcMain.handle('config:getAppConfig', async () => {
    try {
      return APP_CONFIG
    } catch (error) {
      console.error('❌ IPC Error - config:getAppConfig:', error)
      throw error
    }
  })

  console.log('✅ Configuration IPC handlers registered')
}
