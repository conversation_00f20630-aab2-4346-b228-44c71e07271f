/**
 * RabbitMQ IPC Handlers
 * Handles all RabbitMQ-related IPC communication
 */

import { ipcMain } from 'electron'
import rabbitmqService from '../rabbitmq.js'

export function rabbitmqHandlers() {
  // Get RabbitMQ status
  ipcMain.handle('rabbitmq:getStatus', async () => {
    try {
      return rabbitmqService.getStatus()
    } catch (error) {
      console.error('❌ IPC Error - rabbitmq:getStatus:', error)
      throw error
    }
  })

  // Publish message to RabbitMQ
  ipcMain.handle('rabbitmq:publishMessage', async (_, routingKey, message) => {
    try {
      return await rabbitmqService.publishMessage(routingKey, message)
    } catch (error) {
      console.error('❌ IPC Error - rabbitmq:publishMessage:', error)
      throw error
    }
  })

  console.log('✅ RabbitMQ IPC handlers registered')
}
