import { app, BrowserWindow } from 'electron'
import path from 'path'
import { fileURLToPath, URL } from 'url'
import authService from './auth.js'
import { APP_CONFIG, PROJECT_CONFIG } from './config.js'
import { initDatabase } from './database.js'
import { setupIpcHandlers } from './ipc/index.js'
import rabbitmqService from './rabbitmq.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let mainWindow

function createWindow() {
  console.log('🪟 Creating Electron window...')

  const preloadPath = path.join(__dirname, 'preload.js')
  console.log('🔧 Preload script path:', preloadPath)

  mainWindow = new BrowserWindow({
    width: APP_CONFIG.window.width,
    height: APP_CONFIG.window.height,
    title: PROJECT_CONFIG.name,
    fullscreen: APP_CONFIG.window.fullscreen,
    webPreferences: {
      nodeIntegration: false, // Secure: disable node integration
      contextIsolation: true, // Secure: enable context isolation
      webSecurity: true, // Secure: enable web security
      allowRunningInsecureContent: false, // Secure: disable insecure content
      experimentalFeatures: false, // Secure: disable experimental features
      preload: preloadPath, // Secure preload script for IPC
    },
  })

  // No remote module needed - using secure IPC

  console.log('🪟 Electron window created')

  // Add event listeners for debugging
  mainWindow.webContents.on('did-start-loading', () => {
    console.log('🔄 Window started loading...')
  })

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Window finished loading')
  })

  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('❌ Window failed to load:', errorCode, errorDescription)
  })

  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Load application based on NODE_ENV
  if (process.env.NODE_ENV === 'development') {
    const devUrl = 'http://localhost:5174'
    console.log('🔗 Loading development URL:', devUrl)
    mainWindow.loadURL(devUrl)

    // Open DevTools automatically in development
    console.log('🛠️ Opening dev tools...')
    mainWindow.webContents.openDevTools()
  } else {
    // Load built application
    const prodPath = path.join(__dirname, '../../dist/index.html')
    console.log('🔗 Loading production file:', prodPath)
    mainWindow.loadFile(prodPath)
  }

  // Security: Prevent new window creation
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log('🚫 Blocked new window creation for:', url)
    return { action: 'deny' }
  })
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  console.log(`🚀 Starting ShopigoFurpa v${PROJECT_CONFIG.version}`)

  // Setup secure IPC communication
  setupIpcHandlers()

  // Initialize database
  try {
    await initDatabase()
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    // Continue anyway - might be able to run without database
  }

  // Initialize authentication service
  try {
    await authService.initialize()
    console.log('🔐 Authentication service initialized')
  } catch (error) {
    console.error('❌ Authentication service initialization failed:', error)
  }

  // Initialize RabbitMQ connection
  try {
    await rabbitmqService.connect()
  } catch (error) {
    console.error('❌ RabbitMQ connection failed:', error.message)
    console.log('⚠️ RabbitMQ connection failed, continuing without messaging:', error.message)
  }

  // Create the main window
  createWindow()

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Security: Prevent navigation to external URLs
app.on('web-contents-created', (_, contents) => {
  contents.on('will-navigate', (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    // Allow localhost during development
    if (process.env.NODE_ENV === 'development' && parsedUrl.hostname === 'localhost') {
      return
    }

    // Allow file protocol for production builds
    if (parsedUrl.protocol === 'file:') {
      return
    }

    console.log('🚫 Blocked navigation to:', navigationUrl)
    navigationEvent.preventDefault()
  })
})

// Security: Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  // Allow self-signed certificates for localhost during development
  if (process.env.NODE_ENV === 'development' && url.includes('localhost')) {
    event.preventDefault()
    callback(true)
    return
  }

  // In production, use default behavior
  callback(false)
})

// Handle app termination
app.on('before-quit', async () => {
  console.log('🚪 Application shutting down...')

  // Cleanup RabbitMQ connection
  try {
    await rabbitmqService.disconnect()
    console.log('✅ RabbitMQ disconnected successfully')
  } catch (error) {
    console.error('⚠️ Error disconnecting RabbitMQ:', error)
  }
})

// Handle second instance (prevent multiple instances)
app.on('second-instance', () => {
  // Focus the existing window if a second instance is opened
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore()
    }
    mainWindow.focus()
  }
})

// Ensure single instance
if (!app.requestSingleInstanceLock()) {
  console.log('🚫 Another instance is already running')
  app.quit()
}
