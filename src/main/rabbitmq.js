import amqp from 'amqplib'
import { RABBITMQ_CONFIG } from './config.js'

class RabbitMQService {
  constructor() {
    this.connection = null
    this.channel = null
    this.isConnected = false
  }

  async connect() {
    try {
      console.log('Connecting to RabbitMQ...', RABBITMQ_CONFIG.url.replace(/:.*@/, ':***@'))

      this.connection = await amqp.connect(RABBITMQ_CONFIG.url, {
        heartbeat: RABBITMQ_CONFIG.heartbeat,
        timeout: RABBITMQ_CONFIG.connectionTimeout,
      })

      this.channel = await this.connection.createChannel()
      this.isConnected = true

      console.log('✅ RabbitMQ connected successfully')

      // Setup connection event handlers
      this.connection.on('error', err => {
        console.error('❌ RabbitMQ connection error:', err)
        this.isConnected = false
      })

      this.connection.on('close', () => {
        console.log('🔌 RabbitMQ connection closed')
        this.isConnected = false
      })

      // Setup exchanges and queues
      await this.setupExchangesAndQueues()

      return true
    } catch (error) {
      console.error('❌ Failed to connect to RabbitMQ:', error.message)
      this.isConnected = false
      return false
    }
  }

  async setupExchangesAndQueues() {
    if (!this.channel) {
      throw new Error('Channel not available')
    }

    try {
      // Create exchange
      await this.channel.assertExchange(
        RABBITMQ_CONFIG.exchanges.main,
        RABBITMQ_CONFIG.exchanges.type,
        { durable: true }
      )

      // Create queues
      const queues = Object.values(RABBITMQ_CONFIG.queues)
      for (const queueName of queues) {
        await this.channel.assertQueue(queueName, { durable: true })

        // Bind queue to exchange with routing key
        await this.channel.bindQueue(queueName, RABBITMQ_CONFIG.exchanges.main, queueName)
      }

      console.log('✅ RabbitMQ exchanges and queues setup completed')
    } catch (error) {
      console.error('❌ Failed to setup exchanges and queues:', error)
      throw error
    }
  }

  async publishMessage(routingKey, message, options = {}) {
    if (!this.isConnected || !this.channel) {
      throw new Error('RabbitMQ not connected')
    }

    try {
      const messageBuffer = Buffer.from(JSON.stringify(message))

      const result = await this.channel.publish(
        RABBITMQ_CONFIG.exchanges.main,
        routingKey,
        messageBuffer,
        {
          persistent: true,
          timestamp: Date.now(),
          ...options,
        }
      )

      console.log(`📤 Message published to ${routingKey}:`, message)
      return result
    } catch (error) {
      console.error('❌ Failed to publish message:', error)
      throw error
    }
  }

  async consumeMessages(queueName, callback, options = {}) {
    if (!this.isConnected || !this.channel) {
      throw new Error('RabbitMQ not connected')
    }

    try {
      await this.channel.consume(
        queueName,
        msg => {
          if (msg) {
            try {
              const content = JSON.parse(msg.content.toString())
              console.log(`📥 Message received from ${queueName}:`, content)

              // Call the callback function
              callback(content, msg)

              // Acknowledge the message
              this.channel.ack(msg)
            } catch (error) {
              console.error('❌ Error processing message:', error)
              // Reject the message and don't requeue
              this.channel.nack(msg, false, false)
            }
          }
        },
        {
          noAck: false,
          ...options,
        }
      )

      console.log(`👂 Started consuming messages from ${queueName}`)
    } catch (error) {
      console.error('❌ Failed to consume messages:', error)
      throw error
    }
  }

  async sendToQueue(queueName, message) {
    if (!this.isConnected || !this.channel) {
      throw new Error('RabbitMQ not connected')
    }

    try {
      const messageBuffer = Buffer.from(JSON.stringify(message))

      const result = await this.channel.sendToQueue(queueName, messageBuffer, { persistent: true })

      console.log(`📤 Message sent to queue ${queueName}:`, message)
      return result
    } catch (error) {
      console.error('❌ Failed to send message to queue:', error)
      throw error
    }
  }

  async disconnect() {
    try {
      if (this.channel) {
        await this.channel.close()
        this.channel = null
      }

      if (this.connection) {
        await this.connection.close()
        this.connection = null
      }

      this.isConnected = false
      console.log('🔌 RabbitMQ disconnected')
    } catch (error) {
      console.error('❌ Error disconnecting from RabbitMQ:', error)
    }
  }

  getStatus() {
    return {
      isConnected: this.isConnected,
      hasConnection: !!this.connection,
      hasChannel: !!this.channel,
      config: {
        host: RABBITMQ_CONFIG.host,
        port: RABBITMQ_CONFIG.port,
        vhost: RABBITMQ_CONFIG.vhost,
        queues: RABBITMQ_CONFIG.queues,
        exchanges: RABBITMQ_CONFIG.exchanges,
      },
    }
  }
}

// Create singleton instance
const rabbitmqService = new RabbitMQService()

export default rabbitmqService
