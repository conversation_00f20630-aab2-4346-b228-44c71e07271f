<script>
  console.log('🔥 TestKeyboard component loaded!')
</script>

<!-- Test Component -->
<div style="position: fixed; top: 50px; right: 10px; background: green; color: white; padding: 20px; z-index: 10000; font-size: 16px; border: 3px solid blue;">
  ✅ TEST KEYBOARD COMPONENT WORKING!
</div>

<!-- Test Button -->
<button 
  style="position: fixed; bottom: 20px; right: 20px; width: 60px; height: 60px; border-radius: 50%; background: orange; color: white; border: none; font-size: 24px; cursor: pointer; z-index: 9999;"
  on:click={() => alert('Test button clicked!')}
>
  🎹
</button>
