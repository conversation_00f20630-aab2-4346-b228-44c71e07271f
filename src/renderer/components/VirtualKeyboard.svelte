<script>
  import { onDestroy, onMount } from 'svelte'
  import { writable } from 'svelte/store'

  // Dynamic import to avoid SSR issues
  let Keyboard = null

  // Stores for state management
  export const keyboardVisible = writable(false)
  export const keyboardLayout = writable('turkish-q')
  export const keyboardMode = writable('alphabetic') // 'alphabetic' or 'numeric'

  let keyboardContainer
  let keyboard
  let currentInput = null
  let isVisible = false
  let currentLayout = 'turkish-q'
  let currentMode = 'alphabetic'
  let isLoginRoute = false

  // Screen resolution and fullscreen detection
  let screenInfo = {
    width: 0,
    height: 0,
    isFullscreen: false,
    devicePixelRatio: 1,
    orientation: 'landscape',
  }

  // Dynamic keyboard sizing based on screen resolution
  let keyboardScale = {
    width: 1,
    height: 1,
    fontSize: 1,
  }

  console.log('🚀 VirtualKeyboard component script loaded!')

  // Flags to prevent auto-close during keyboard interaction
  const keyboardState = {
    isInteracting: false,
    blurTimeoutId: null,
  }

  // Screen resolution detection and fullscreen monitoring
  function detectScreenInfo() {
    const width = window.innerWidth
    const height = window.innerHeight
    const devicePixelRatio = window.devicePixelRatio || 1
    const isFullscreen =
      window.innerHeight === window.screen.height ||
      document.fullscreenElement !== null ||
      document.webkitFullscreenElement !== null ||
      document.mozFullScreenElement !== null ||
      document.msFullscreenElement !== null

    const orientation = width > height ? 'landscape' : 'portrait'

    screenInfo = {
      width,
      height,
      isFullscreen,
      devicePixelRatio,
      orientation,
    }

    console.log('📱 Screen info detected:', screenInfo)
    calculateKeyboardScale()
  }

  // Calculate optimal keyboard scaling based on screen resolution
  function calculateKeyboardScale() {
    const { width, height, isFullscreen, orientation } = screenInfo

    // Base scaling factors for different screen categories
    let baseScale = 1
    let heightScale = 1.3 // Maintain 30% height increase
    let fontScale = 1

    // Ultra-high resolution displays (4K+)
    if (width >= 3840 || height >= 2160) {
      baseScale = 3.5
      fontScale = 1.8
    }
    // High resolution displays (2K+)
    else if (width >= 2560 || height >= 1440) {
      baseScale = 2.8
      fontScale = 1.6
    }
    // Large displays (1920x1080+)
    else if (width >= 1920 && height >= 1080) {
      baseScale = 2.2
      fontScale = 1.4
    }
    // Medium displays (1366x768 to 1920x1080)
    else if (width >= 1366 && height >= 768) {
      baseScale = 1.8
      fontScale = 1.2
    }
    // Small displays (1024x768 to 1366x768)
    else if (width >= 1024 && height >= 768) {
      baseScale = 1.5
      fontScale = 1.1
    }
    // Tablet displays (768x1024 or similar)
    else if (width >= 768 || height >= 768) {
      baseScale = 1.2
      fontScale = 1.0
    }
    // Mobile displays
    else {
      baseScale = 1.0
      fontScale = 0.9
    }

    // Adjust for fullscreen mode
    if (isFullscreen) {
      baseScale *= 1.1 // Slightly larger in fullscreen
      heightScale *= 1.05
    }

    // Adjust for orientation
    if (orientation === 'portrait' && width < 768) {
      baseScale *= 0.9 // Smaller on mobile portrait
      heightScale *= 0.95
    }

    keyboardScale = {
      width: baseScale,
      height: heightScale,
      fontSize: fontScale,
    }

    console.log('⚖️ Keyboard scale calculated:', keyboardScale)
  }

  // Reactive statement to detect screen changes and update keyboard
  $: if (screenInfo.width > 0) {
    console.log('📱 Screen info updated, recalculating keyboard scale')
    calculateKeyboardScale()

    // Update keyboard if it exists
    if (keyboard && isVisible) {
      console.log('🔄 Updating keyboard for new screen dimensions')
      // Force re-render by updating the keyboard container classes
      if (keyboardContainer) {
        keyboardContainer.style.setProperty('--keyboard-scale-width', keyboardScale.width)
        keyboardContainer.style.setProperty('--keyboard-scale-height', keyboardScale.height)
        keyboardContainer.style.setProperty('--keyboard-scale-font', keyboardScale.fontSize)
      }
    }
  }

  // Reactive statement to initialize keyboard when container is ready
  $: if (keyboardContainer && Keyboard && !keyboard) {
    console.log('🔄 Container ready, initializing keyboard...')
    console.log(
      '🔍 Reactive state - Container:',
      !!keyboardContainer,
      'Keyboard class:',
      !!Keyboard,
      'Instance:',
      !!keyboard
    )
    initializeKeyboard()
  }

  // Reactive statement to handle keyboard cleanup when hidden
  $: if (!isVisible && keyboard) {
    console.log('🧹 Keyboard hidden, cleaning up instance...')
    cleanupKeyboard()
  }

  // Subscribe to stores
  keyboardVisible.subscribe(value => {
    console.log('👁️ Keyboard visibility changed:', value, '(previous:', isVisible, ')')
    isVisible = value
  })

  keyboardLayout.subscribe(value => {
    currentLayout = value
    if (keyboard) {
      keyboard.setOptions({
        layout: getLayoutConfig(value, currentMode),
      })
    }
  })

  keyboardMode.subscribe(value => {
    currentMode = value
    console.log('🔢 Keyboard mode changed:', value)
    if (keyboard) {
      keyboard.setOptions({
        layout: getLayoutConfig(currentLayout, value),
      })
    }
  })

  // Turkish keyboard layouts
  const turkishQLayout = {
    default: [
      '1 2 3 4 5 6 7 8 9 0 * - {bksp}',
      '{tab} q w e r t y u ı o p ğ ü [ ]',
      '{caps} a s d f g h j k l ş i , {enter}',
      '{shift} < z x c v b n m ö ç . {shift}',
      '{space}',
    ],
    shift: [
      '! " ^ + % & / ( ) = ? _ {bksp}',
      '{tab} Q W E R T Y U I O P Ğ Ü { }',
      '{caps} A S D F G H J K L Ş İ ; {enter}',
      '{shift} > Z X C V B N M Ö Ç : {shift}',
      '{space}',
    ],
  }

  const turkishFLayout = {
    default: [
      '1 2 3 4 5 6 7 8 9 0 / - {bksp}',
      '{tab} f g ğ ı o d r n h p q w x',
      '{caps} u i e a ü t k m l y ş {enter}',
      '{shift} j ö v c ç z s b . , {shift}',
      '{space}',
    ],
    shift: [
      '! " ^ + % & / ( ) = ? _ {bksp}',
      '{tab} F G Ğ I O D R N H P Q W X',
      '{caps} U İ E A Ü T K M L Y Ş {enter}',
      '{shift} J Ö V C Ç Z S B : ; {shift}',
      '{space}',
    ],
  }

  // Numeric keypad layout - Turkish style with comma as decimal separator
  const numericLayout = {
    default: ['7 8 9 {bksp}', '4 5 6 /', '1 2 3 *', '0 , . {enter}', '+ - = {space}'],
  }

  function getLayoutConfig(layout, mode = 'alphabetic') {
    if (mode === 'numeric') {
      return numericLayout
    }
    return layout === 'turkish-f' ? turkishFLayout : turkishQLayout
  }

  // Check if current route is login
  function updateRouteContext() {
    if (typeof window !== 'undefined') {
      const currentRoute = window.location.hash.slice(1) || '/'
      isLoginRoute = currentRoute === '/login'
    }
  }

  function cleanupKeyboard() {
    console.log('🧹 Cleaning up keyboard instance...')
    if (keyboard) {
      try {
        console.log('🗑️ Destroying keyboard instance')
        keyboard.destroy()
        keyboard = null
        console.log('✅ Keyboard cleanup completed')
      } catch (error) {
        console.error('❌ Error during keyboard cleanup:', error)
        keyboard = null // Reset anyway
      }
    }
  }

  function isKeyboardValid() {
    if (!keyboard) return false

    // Check if the keyboard's DOM element still exists and is connected
    try {
      const keyboardElement = keyboardContainer?.querySelector('.hg-theme-default')
      const isConnected = keyboardElement && keyboardElement.isConnected
      console.log(
        '🔍 Keyboard validation - Element exists:',
        !!keyboardElement,
        'Connected:',
        isConnected
      )
      return isConnected
    } catch (error) {
      console.warn('⚠️ Error validating keyboard:', error)
      return false
    }
  }

  async function initializeKeyboard() {
    console.log('🔧 Initializing keyboard...')
    console.log(
      '🔍 State check - Container:',
      !!keyboardContainer,
      'Keyboard class:',
      !!Keyboard,
      'Current instance:',
      !!keyboard
    )

    if (!keyboardContainer) {
      console.error('❌ Keyboard container not found')
      return
    }

    if (!Keyboard) {
      console.error('❌ Keyboard class not loaded')
      return
    }

    // Clean up any existing invalid keyboard instance
    if (keyboard && !isKeyboardValid()) {
      console.log('🔄 Existing keyboard is invalid, cleaning up...')
      cleanupKeyboard()
    }

    try {
      console.log('🏗️ Creating new keyboard instance...')
      keyboard = new Keyboard(keyboardContainer, {
        onChange: input => onChange(input),
        onKeyPress: button => onKeyPress(button),
        layout: getLayoutConfig(currentLayout, currentMode),
        display: {
          '{bksp}': '⌫',
          '{enter}': '↵',
          '{shift}': '⇧',
          '{tab}': '⇥',
          '{caps}': '⇪',
          '{space}': ' ',
        },
        buttonTheme: [
          {
            class: 'hg-red',
            buttons: '{bksp}',
          },
          {
            class: 'hg-blue',
            buttons: '{enter} {tab}',
          },
        ],
        mergeDisplay: true,
        debug: true, // Enable debug for troubleshooting
      })

      console.log('✅ Keyboard initialized successfully:', keyboard)

      // Add event listeners to keyboard buttons to prevent auto-close
      setTimeout(() => {
        const keyboardElement = keyboardContainer?.querySelector('.hg-theme-default')
        console.log('🔍 Post-init verification - Keyboard DOM element:', !!keyboardElement)

        if (keyboardElement) {
          setupKeyboardInteractionListeners(keyboardElement)
        }
      }, 100)
    } catch (error) {
      console.error('❌ Error initializing keyboard:', error)
      keyboard = null
    }
  }

  function setupKeyboardInteractionListeners(keyboardElement) {
    console.log('🎹 Setting up keyboard interaction listeners')

    // Add event listeners to maintain focus and prevent auto-close
    const buttons = keyboardElement.querySelectorAll('.hg-button')
    console.log('🔘 Found', buttons.length, 'keyboard buttons')

    buttons.forEach(button => {
      // CRITICAL FIX: Prevent keyboard buttons from stealing focus
      button.addEventListener('mousedown', event => {
        console.log(
          '🖱️ Keyboard button mousedown:',
          button.textContent,
          'Current input:',
          currentInput?.tagName
        )

        // Prevent the button from taking focus away from input field
        event.preventDefault()

        // Set interaction flag to prevent auto-close
        keyboardState.isInteracting = true

        console.log('🚫 Prevented focus theft from keyboard button')
      })

      // Ensure input field maintains focus after keyboard interaction
      button.addEventListener('mouseup', () => {
        console.log('🖱️ Keyboard button mouseup - ensuring input focus')

        // Immediately restore focus to input field if it was lost
        if (currentInput) {
          // Force focus back to input field
          setTimeout(() => {
            if (document.activeElement !== currentInput) {
              console.log('🎯 Restoring focus to input field after keyboard click')
              currentInput.focus()
            }

            // Clear interaction flag
            keyboardState.isInteracting = false
            console.log('✅ Input focus maintained after keyboard interaction')
          }, 10) // Very short delay to ensure the click is processed
        }
      })

      // Also handle click event to ensure focus is maintained
      button.addEventListener('click', event => {
        console.log('🖱️ Keyboard button clicked:', button.textContent)

        // Prevent any default behavior that might steal focus
        event.preventDefault()

        // Ensure input field maintains focus
        if (currentInput) {
          setTimeout(() => {
            if (document.activeElement !== currentInput) {
              console.log('🎯 Refocusing input after keyboard button click')
              currentInput.focus()
            }
          }, 5)
        }
      })
    })
  }

  function onChange(input) {
    console.log(
      '📝 Keyboard input changed:',
      input,
      'Current input element:',
      currentInput?.tagName,
      currentInput?.type,
      'Active element:',
      document.activeElement?.tagName
    )

    if (currentInput) {
      console.log('🔄 Setting input value from:', currentInput.value, 'to:', input)

      // Store the current input reference to ensure it doesn't get lost
      const targetInput = currentInput

      // Update the input value
      targetInput.value = input

      // Trigger input event for form validation
      // eslint-disable-next-line no-undef
      targetInput.dispatchEvent(new Event('input', { bubbles: true }))

      // CRITICAL: Immediately ensure input field maintains focus
      if (document.activeElement !== targetInput) {
        console.log('🎯 CRITICAL: Restoring focus to input field after value change')
        targetInput.focus()
      }

      // Double-check focus after a micro-delay
      setTimeout(() => {
        if (document.activeElement !== targetInput) {
          console.log('🚨 EMERGENCY: Re-focusing input field')
          targetInput.focus()
        }
      }, 1)

      console.log('✅ Input value updated, event dispatched, and focus aggressively maintained')
    } else {
      console.warn('⚠️ No current input element to update!')

      // Try to find a focused input field as fallback
      const activeElement = document.activeElement
      if (
        activeElement &&
        (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
      ) {
        console.log('🔄 Using currently focused element as fallback')
        currentInput = activeElement
        activeElement.value = input
        // eslint-disable-next-line no-undef
        activeElement.dispatchEvent(new Event('input', { bubbles: true }))

        // Ensure the fallback input maintains focus
        if (document.activeElement !== activeElement) {
          activeElement.focus()
        }

        console.log('✅ Fallback input update successful with focus maintained')
      } else {
        console.error('❌ No input field available for keyboard input!')
      }
    }
  }

  function onKeyPress(button) {
    console.log('⌨️ Key pressed:', button)
    if (button === '{shift}' || button === '{caps}') {
      handleShift()
    } else if (button === '{enter}') {
      handleEnterKey()
    }
  }

  function handleEnterKey() {
    console.log('⌨️ Enter key pressed on virtual keyboard')
    console.log('⌨️ Current input:', currentInput)

    if (currentInput) {
      // Check if this is a search input (by placeholder or class)
      const isSearchInput =
        currentInput.placeholder?.includes('arama') ||
        currentInput.placeholder?.includes('search') ||
        currentInput.classList?.contains('search-input')

      console.log('⌨️ Is search input:', isSearchInput)
      console.log('⌨️ Input placeholder:', currentInput.placeholder)
      console.log('⌨️ Input value:', currentInput.value)

      if (isSearchInput && currentInput.value.trim()) {
        console.log('🔍 Performing direct search from virtual keyboard')
        performDirectSearch(currentInput.value.trim())
      } else {
        // Create and dispatch Enter key event for non-search inputs
        const enterEvent = new KeyboardEvent('keypress', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true,
        })

        console.log('⌨️ Dispatching Enter key event to input')
        currentInput.dispatchEvent(enterEvent)

        // Also try keydown event for better compatibility
        const keydownEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true,
        })

        currentInput.dispatchEvent(keydownEvent)
        console.log('⌨️ Enter key events dispatched')
      }
    }
  }

  async function performDirectSearch(searchTerm) {
    console.log('🔍 Direct search initiated for:', searchTerm)

    try {
      // Import database utility
      const { searchInventory } = await import('../utils/database.js')
      console.log('🔍 Database utility imported')

      // Perform search
      const results = searchInventory(searchTerm)
      console.log('🔍 Search results:', results)

      if (results && results.length > 0) {
        const item = results[0]
        console.log('🔍 Selected item:', item)

        // Create a custom event to notify the application
        const searchEvent = new CustomEvent('virtualKeyboardSearch', {
          detail: {
            searchTerm,
            item,
            results,
          },
          bubbles: true,
        })

        console.log('🔍 Dispatching search event')
        document.dispatchEvent(searchEvent)

        // Clear search input
        if (currentInput) {
          currentInput.value = ''
          // Trigger input event
          currentInput.dispatchEvent(new Event('input', { bubbles: true }))
        }

        // Show success notification
        showSearchNotification(`${item.name} bulundu!`, 'success')
      } else {
        console.log('❌ No results found')
        showSearchNotification('Ürün bulunamadı', 'error')
      }
    } catch (error) {
      console.error('❌ Direct search error:', error)
      showSearchNotification('Arama sırasında bir hata oluştu', 'error')
    }
  }

  function showSearchNotification(message, type) {
    console.log(`📢 Notification: ${message} (${type})`)

    // Create a simple notification element
    const notification = document.createElement('div')
    notification.textContent = message
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      z-index: 10000;
      background-color: ${type === 'success' ? '#48c774' : '#f14668'};
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `

    document.body.appendChild(notification)

    // Remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 3000)
  }

  function handleShift() {
    const currentLayout = keyboard.options.layoutName
    const shiftToggle = currentLayout === 'default' ? 'shift' : 'default'
    keyboard.setOptions({
      layoutName: shiftToggle,
    })
  }

  function setupInputListeners() {
    const inputs = document.querySelectorAll(
      'input[type="text"], input[type="email"], input[type="password"], input[type="number"], textarea'
    )

    inputs.forEach(input => {
      // Only add blur listener - remove focus listener to prevent automatic opening
      input.addEventListener('blur', handleInputBlur)
      // Add focus listener only for tracking current input, not for opening keyboard
      input.addEventListener('focus', handleInputFocusTracking)
    })
  }

  function handleInputFocusTracking(event) {
    console.log(
      '🎯 Input focused (tracking only):',
      event.target.type,
      event.target.name || event.target.id,
      'Keyboard currently visible:',
      isVisible
    )

    // Update current input reference for manual keyboard usage
    currentInput = event.target

    // Auto-detect numeric mode for number input fields (but don't open keyboard)
    const isNumberInput = event.target.type === 'number'
    const shouldUseNumeric = isNumberInput

    if (shouldUseNumeric && currentMode !== 'numeric') {
      console.log('🔢 Auto-switching to numeric mode for number input')
      keyboardMode.set('numeric')
    } else if (!shouldUseNumeric && currentMode !== 'alphabetic') {
      console.log('🔤 Auto-switching to alphabetic mode for text input')
      keyboardMode.set('alphabetic')
    }

    // Clear any pending blur timeout
    if (keyboardState.blurTimeoutId) {
      clearTimeout(keyboardState.blurTimeoutId)
      keyboardState.blurTimeoutId = null
      console.log('🚫 Cleared pending blur timeout')
    }

    // If keyboard is already visible, just switch context to new input
    if (isVisible && keyboard) {
      console.log('🔄 Keyboard already visible - switching to new input field')
      keyboard.setInput(currentInput.value)
      console.log('✅ Keyboard context switched to new input')
    }
    // NOTE: Removed automatic keyboard opening - keyboard only opens via manual toggle
  }

  function handleInputBlur(event) {
    console.log(
      '👋 Input blur detected:',
      event.target.type,
      'Keyboard visible:',
      isVisible,
      'Interacting:',
      keyboardState.isInteracting
    )

    // PERSISTENT MODE: Always prevent auto-close regardless of interaction state
    // Keyboard only closes via manual actions (close button, toggle, click outside)
    console.log('🔒 PERSISTENT KEYBOARD: Ignoring all blur events')

    // Clear any timeouts
    if (keyboardState.blurTimeoutId) {
      clearTimeout(keyboardState.blurTimeoutId)
      keyboardState.blurTimeoutId = null
    }

    // Always return early - never close on blur
    return
  }

  function toggleKeyboard() {
    console.log(
      '🔄 Toggle keyboard clicked - Current state:',
      isVisible,
      'Current input:',
      currentInput?.tagName
    )

    // Store the currently focused element before toggle
    const activeElement = document.activeElement
    const wasInputFocused =
      activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')

    console.log(
      '🎯 Active element before toggle:',
      activeElement?.tagName,
      'Is input:',
      wasInputFocused
    )

    keyboardVisible.update(visible => {
      const newState = !visible
      console.log('🔄 Updating keyboard visibility:', visible, '->', newState)

      // Track manual operations
      if (newState) {
        console.log('📖 Keyboard manually opened')

        // If opening keyboard and there's a focused input, preserve it
        if (wasInputFocused && activeElement) {
          console.log('🔗 Preserving input focus during keyboard open')
          currentInput = activeElement

          // Restore focus after a short delay to ensure keyboard is rendered
          setTimeout(() => {
            if (activeElement && activeElement.isConnected) {
              activeElement.focus()
              console.log('✅ Input focus restored after keyboard open')

              // Update keyboard with current input value
              if (keyboard) {
                keyboard.setInput(activeElement.value)
                console.log('🔄 Keyboard synced with input value:', activeElement.value)
              }
            }
          }, 100)
        }
      } else {
        console.log('📕 Keyboard manually closed')
      }

      return newState
    })
  }

  function closeKeyboard() {
    console.log('❌ Explicitly closing keyboard')
    keyboardVisible.set(false)
    currentInput = null
  }

  function toggleLayout() {
    keyboardLayout.update(layout => (layout === 'turkish-q' ? 'turkish-f' : 'turkish-q'))
  }

  function toggleMode() {
    keyboardMode.update(mode => {
      const newMode = mode === 'alphabetic' ? 'numeric' : 'alphabetic'
      console.log('🔄 Toggling keyboard mode:', mode, '->', newMode)
      return newMode
    })
  }

  onMount(async () => {
    updateRouteContext()

    // Initialize screen detection
    detectScreenInfo()

    // Dynamic import of simple-keyboard
    try {
      const keyboardModule = await import('simple-keyboard')
      Keyboard = keyboardModule.default

      // Import CSS - try multiple methods
      try {
        await import('simple-keyboard/build/css/index.css')
        console.log('✅ CSS imported via import()')
      } catch (cssError) {
        console.log('⚠️ CSS import failed, trying alternative method:', cssError.message)
        // Fallback: Add CSS link manually
        const cssLink = document.createElement('link')
        cssLink.rel = 'stylesheet'
        cssLink.href = 'https://cdn.jsdelivr.net/npm/simple-keyboard@latest/build/css/index.css'
        document.head.appendChild(cssLink)
        console.log('✅ CSS loaded from CDN')
      }

      console.log('✅ Simple-keyboard loaded successfully')

      // Keyboard will be initialized by reactive statement when container is ready
    } catch (error) {
      console.error('❌ Failed to load simple-keyboard:', error)
    }

    setupInputListeners()

    // Screen resolution and fullscreen monitoring
    const handleResize = () => {
      console.log('📱 Window resized, updating screen info')
      detectScreenInfo()
    }

    const handleFullscreenChange = () => {
      console.log('🖥️ Fullscreen state changed')
      detectScreenInfo()
    }

    const handleOrientationChange = () => {
      console.log('🔄 Orientation changed')
      setTimeout(detectScreenInfo, 100) // Delay to ensure dimensions are updated
    }

    // Add event listeners for screen changes
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    // Listen for route changes
    const handleHashChange = () => {
      updateRouteContext()
      // Re-setup input listeners for new page content
      setTimeout(setupInputListeners, 100)
    }

    window.addEventListener('hashchange', handleHashChange)

    // Add selective click-outside handler to close keyboard
    const handleClickOutside = event => {
      if (isVisible && keyboardContainer) {
        const keyboardOverlay = keyboardContainer.closest('.virtual-keyboard-overlay')
        const toggleButton = document.querySelector('.virtual-keyboard-toggle')

        // Check if clicked element is an input field
        const isInputElement =
          event.target && (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA')

        // Only close if click is outside keyboard AND not on an input field
        if (
          keyboardOverlay &&
          !keyboardOverlay.contains(event.target) &&
          !toggleButton?.contains(event.target) &&
          !isInputElement
        ) {
          console.log('🖱️ Click outside detected (non-input) - closing keyboard')
          closeKeyboard()
        } else if (isInputElement) {
          console.log('🎯 Click on input field detected - keeping keyboard open')
        }
      }
    }

    document.addEventListener('click', handleClickOutside)

    // Re-setup listeners when DOM changes
    // eslint-disable-next-line no-undef
    const observer = new MutationObserver(() => {
      setupInputListeners()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => {
      window.removeEventListener('hashchange', handleHashChange)
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
      document.removeEventListener('click', handleClickOutside)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
      observer.disconnect()
    }
  })

  onDestroy(() => {
    if (keyboard) {
      keyboard.destroy()
    }
  })
</script>

<!-- Debug Info (temporary) -->

<!-- Toggle Button -->
<button
  class="virtual-keyboard-toggle {isLoginRoute ? 'login-theme' : 'dark-theme'}"
  on:click={toggleKeyboard}
  on:mousedown={event => {
    // Prevent the button from stealing focus from input fields
    event.preventDefault()
    console.log('🚫 Toggle button mousedown prevented - preserving input focus')
  }}
  title="Toggle Virtual Keyboard"
>
  <i class="fas fa-keyboard"></i>
</button>

<!-- Virtual Keyboard Overlay -->
{#if isVisible}
  <div class="virtual-keyboard-overlay {isLoginRoute ? 'login-theme' : 'dark-theme'}">
    <div class="virtual-keyboard-header">
      <div class="virtual-keyboard-controls">
        <div class="control-buttons-left">
          {#if currentMode === 'alphabetic'}
            <button class="layout-toggle-btn" on:click={toggleLayout} title="Switch Layout (Q/F)">
              {currentLayout === 'turkish-q' ? 'Q' : 'F'}
            </button>
          {/if}
          <button class="mode-toggle-btn" on:click={toggleMode} title="Switch Mode (ABC/123)">
            {currentMode === 'numeric' ? 'ABC' : '123'}
          </button>
        </div>

        <button class="close-btn" on:click={closeKeyboard} title="Close Keyboard">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
    <div class="virtual-keyboard-container" bind:this={keyboardContainer}></div>
  </div>
{/if}

<style>
  /* HIGHEST Z-INDEX LAYER - COMPLETE UI INDEPENDENCE */
  /* Toggle Button Styles - Topmost Layer */
  .virtual-keyboard-toggle {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    border: none !important;
    cursor: pointer !important;
    z-index: 2147483647 !important; /* Maximum z-index value for complete independence */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
    /* Ensure complete isolation from other elements */
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    /* Prevent any parent styling interference */
    background-clip: padding-box !important;
    isolation: isolate !important;
  }

  .virtual-keyboard-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .virtual-keyboard-toggle.login-theme {
    background-color: rgba(0, 123, 255, 0.9);
    color: white;
  }

  /* Keyboard Overlay Styles - COMPLETE UI INDEPENDENCE WITH DYNAMIC SCALING */
  .virtual-keyboard-overlay {
    position: fixed !important;
    bottom: 0px !important; /* Position at bottom of viewport */
    left: 50% !important; /* Center horizontally */
    transform: translateX(-50%) !important; /* Perfect centering */
    width: calc(
      var(--keyboard-scale-width, 2) * 600px
    ) !important; /* Dynamic width based on screen */
    max-width: calc(100vw - 20px) !important; /* Ensure it fits on screen */
    max-height: calc(100vh * 0.6) !important; /* Maximum 60% of viewport height */
    min-height: 200px !important; /* Minimum usable height */
    border-radius: 16px 16px 0 0 !important; /* Only round top corners */
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important; /* Enhanced shadow for prominence */
    z-index: 2147483646 !important; /* Just below toggle button */
    animation: slideUp 0.3s ease-out !important;
    overflow: hidden !important; /* Prevent content overflow */
    /* Complete isolation from other UI elements */
    margin: 0 !important;
    padding: 0 !important;
    background-clip: padding-box !important;
    isolation: isolate !important;
    /* Ensure it's always visible and independent */
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    /* CSS custom properties for dynamic scaling */
    --keyboard-scale-width: var(--keyboard-scale-width, 2);
    --keyboard-scale-height: var(--keyboard-scale-height, 1.3);
    --keyboard-scale-font: var(--keyboard-scale-font, 1);
  }

  .virtual-keyboard-overlay.login-theme {
    background-color: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 123, 255, 0.3);
    backdrop-filter: blur(10px);
  }

  /* Header Styles - SCALED UP */
  .virtual-keyboard-header {
    padding: 20px 24px; /* Increased from 12px 16px */
    border-bottom: 2px solid; /* Increased from 1px */
    border-radius: 16px 16px 0 0; /* Increased from 12px */
  }

  .login-theme .virtual-keyboard-header {
    background-color: rgba(0, 123, 255, 0.1);
    border-bottom-color: rgba(0, 123, 255, 0.2);
  }

  .virtual-keyboard-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .control-buttons-left {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .layout-toggle-btn,
  .mode-toggle-btn,
  .close-btn {
    background: none;
    border: 2px solid; /* Increased from 1px */
    border-radius: 8px; /* Increased from 6px */
    padding: 8px 16px; /* Increased from 4px 8px */
    cursor: pointer;
    font-size: 16px; /* Increased from 12px */
    font-weight: bold;
    transition: all 0.2s ease;
    min-width: 50px;
  }

  .login-theme .layout-toggle-btn,
  .login-theme .mode-toggle-btn,
  .login-theme .close-btn {
    color: #333;
    border-color: rgba(0, 123, 255, 0.3);
  }

  .login-theme .layout-toggle-btn:hover,
  .login-theme .mode-toggle-btn:hover,
  .login-theme .close-btn:hover {
    background-color: rgba(0, 123, 255, 0.1);
  }

  /* Keyboard Container - Dynamic Scaling with Complete Independence */
  .virtual-keyboard-container {
    padding: 16px !important;
    border-radius: 0 0 12px 12px !important;
    overflow-y: auto !important; /* Allow vertical scrolling if needed */
    overflow-x: hidden !important; /* Prevent horizontal overflow */
    max-height: calc(100vh * 0.6 - 100px) !important; /* Ensure container fits within overlay */
    min-height: 150px !important; /* Minimum usable height */
    /* Complete isolation */
    position: relative !important;
    z-index: 1 !important;
    margin: 0 !important;
    /* Dynamic scaling support */
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Animation */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Simple Keyboard Theme Customization */
  :global(.light-theme .hg-theme-default) {
    background-color: var(--color-light);
    border-radius: 8px;
  }

  :global(.light-theme .hg-button) {
    background-color: var(--color-light-secondary) !important;
    color: white !important;
    border: 1px solid var(--color-light-trd) !important;
    border-radius: 6px !important;
    margin: 2px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
  }

  :global(.light-theme .hg-button:hover) {
    background-color: var(--color-light-secondary-gradient) !important;
    transform: scale(1.05) !important;
  }

  :global(.light-theme .hg-button:active) {
    background-color: var(--color-theme-light) !important;
    transform: scale(0.95) !important;
  }

  :global(.light-theme .hg-red) {
    background-color: #dc3545 !important;
    color: white !important;
  }

  :global(.light-theme .hg-blue) {
    background-color: var(--color-theme-light) !important;
    color: white !important;
  }

  :global(.login-theme .hg-theme-default) {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
  }

  :global(.login-theme .hg-button) {
    background-color: rgba(0, 123, 255, 0.1) !important;
    color: #333 !important;
    border: 1px solid rgba(0, 123, 255, 0.2) !important;
    border-radius: 6px !important;
    margin: 2px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
  }

  :global(.login-theme .hg-button:hover) {
    background-color: rgba(0, 123, 255, 0.2) !important;
    transform: scale(1.05) !important;
  }

  :global(.login-theme .hg-button:active) {
    background-color: rgba(0, 123, 255, 0.3) !important;
    transform: scale(0.95) !important;
  }

  :global(.login-theme .hg-red) {
    background-color: #dc3545 !important;
    color: white !important;
  }

  :global(.login-theme .hg-blue) {
    background-color: rgba(0, 123, 255, 0.8) !important;
    color: white !important;
  }

  /* Simple Keyboard Base Styles - Fallback */
  :global(.hg-theme-default) {
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px !important;
  }

  :global(.hg-button) {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 6px !important;
    color: #333 !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    margin: 2px !important;
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 8px 12px !important;
    text-align: center !important;
    user-select: none !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }

  :global(.hg-button:hover) {
    background-color: #e9ecef !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  }

  :global(.hg-button:active) {
    background-color: #dee2e6 !important;
    transform: translateY(0) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  :global(.hg-red) {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
  }

  :global(.hg-red:hover) {
    background-color: #c82333 !important;
  }

  :global(.hg-blue) {
    background-color: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
  }

  :global(.hg-blue:hover) {
    background-color: #0056b3 !important;
  }

  /* Ensure keyboard container is visible */
  :global(.simple-keyboard) {
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  }

  :global(.hg-row) {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 6px !important;
  }

  :global(.hg-row:last-child) {
    margin-bottom: 0 !important;
  }

  /* DYNAMIC KEYBOARD SCALING - COMPLETE UI INDEPENDENCE */
  :global(.virtual-keyboard-container .simple-keyboard) {
    transform: scale(var(--keyboard-scale-width, 2)) scaleY(var(--keyboard-scale-height, 1.3)) !important;
    transform-origin: center bottom !important; /* Anchor to bottom for proper positioning */
    margin: calc(var(--keyboard-scale-width, 2) * 10px) 0 calc(var(--keyboard-scale-width, 2) * 5px)
      0 !important;
    /* Complete isolation from parent styling */
    position: relative !important;
    z-index: 1 !important;
    width: 100% !important;
    max-width: none !important;
    /* Ensure proper scaling behavior */
    box-sizing: border-box !important;
    overflow: visible !important;
  }

  /* Enhanced button styling with dynamic font scaling */
  :global(.virtual-keyboard-container .hg-button) {
    font-size: calc(var(--keyboard-scale-font, 1) * 18px) !important; /* Dynamic font scaling */
    font-weight: 500 !important;
    min-height: calc(var(--keyboard-scale-width, 2) * 24px) !important; /* Dynamic touch target */
    border-radius: calc(var(--keyboard-scale-width, 2) * 4px) !important; /* Scaled corners */
    transition: all 0.15s ease !important;
    /* Ensure proper scaling and isolation */
    box-sizing: border-box !important;
    position: relative !important;
  }

  /* Enhanced hover effects for larger buttons */
  :global(.virtual-keyboard-container .hg-button:hover) {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  /* Ensure proper spacing between button rows */
  :global(.virtual-keyboard-container .hg-row) {
    margin-bottom: 8px !important;
  }

  /* Special styling for space bar and larger keys */
  :global(.virtual-keyboard-container .hg-button.hg-standardBtn.hg-button-space) {
    font-size: 16px !important;
  }

  /* DYNAMIC RESPONSIVE DESIGN - SCREEN RESOLUTION ADAPTIVE */
  /* All responsive behavior is now handled by JavaScript screen detection */
  /* CSS custom properties are updated dynamically based on screen resolution */

  /* Fallback media queries for extreme cases only */
  @media (max-width: 320px) {
    .virtual-keyboard-overlay {
      max-width: calc(100vw - 10px) !important;
      min-width: 300px !important;
    }
  }

  @media (max-height: 480px) {
    .virtual-keyboard-overlay {
      max-height: calc(100vh - 50px) !important;
    }
  }

  /* Orientation-specific adjustments */
  @media (orientation: portrait) and (max-width: 768px) {
    .virtual-keyboard-toggle {
      bottom: 15px !important;
      right: 15px !important;
      width: 48px !important;
      height: 48px !important;
      font-size: 18px !important;
    }
  }

  /* FULLSCREEN MODE OPTIMIZATIONS */
  /* Enhanced styling for fullscreen applications */
  :global(body:fullscreen) .virtual-keyboard-overlay,
  :global(body:-webkit-full-screen) .virtual-keyboard-overlay,
  :global(body:-moz-full-screen) .virtual-keyboard-overlay,
  :global(body:-ms-fullscreen) .virtual-keyboard-overlay {
    max-height: calc(100vh * 0.65) !important; /* Slightly larger in fullscreen */
  }

  /* High DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .virtual-keyboard-overlay {
      /* Enhanced rendering for high DPI displays */
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
    }
  }

  /* Accessibility and touch improvements */
  @media (pointer: coarse) {
    :global(.virtual-keyboard-container .hg-button) {
      min-height: calc(var(--keyboard-scale-width, 2) * 28px) !important; /* Larger touch targets */
      padding: calc(var(--keyboard-scale-width, 2) * 4px) !important;
    }
  }
</style>
