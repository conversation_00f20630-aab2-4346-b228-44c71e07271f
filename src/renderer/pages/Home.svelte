<script>
  import { onMount } from 'svelte'
  import { currentUser } from '../stores/authStore.js'
  import { searchInventory as dbSearchInventory, saveSaleTransaction } from '../utils/database.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'
  import './Home.css'
  // Component state
  let searchTerm = ''
  let isSearching = false
  let salesItems = []
  let searchInput
  let sequenceNumber = 1
  let showDLSProducts = false

  // ULTRA BASIT Autocomplete
  let searchResults = []
  let showDropdown = false
  let selectedIndex = -1
  let searchTimeout = null

  // Payment system variables
  let paymentAmount = '₺0,00'
  let rawPaymentAmount = 0
  let selectedPaymentMethod = ''

  // Partial payment system
  let partialPayments = [] // Array to store partial payments
  let totalPaidAmount = 0 // Total amount paid so far
  let remainingAmount = 0 // Remaining amount to be paid

  // Cash payment change screen
  let showCashChangeModal = false
  let cashReceived = 0
  let changeAmount = 0

  // UTILITY FUNCTIONS
  function formatTurkishCurrency(amount) {
    const numericAmount = parseFloat(amount) || 0
    return numericAmount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  function formatQuantity(quantity) {
    return quantity.toLocaleString('tr-TR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })
  }

  function parseQuantityInput(input) {
    return String(input).replace(',', '.')
  }

  function isDecimalUnit(unit) {
    const decimalUnits = ['kg', 'KG', 'Kg', 'kG']
    return decimalUnits.includes(unit)
  }

  function validateQuantityInput(input, unit) {
    if (isDecimalUnit(unit)) {
      const decimalPattern = /^\d+([,]\d{0,2})?$/
      return decimalPattern.test(input)
    } else {
      const integerPattern = /^\d+$/
      return integerPattern.test(input)
    }
  }

  function getQuantityStep(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function getMinimumQuantity(unit) {
    return isDecimalUnit(unit) ? 0.01 : 1
  }

  function handleQuantityKeydown(event, unit) {
    const key = event.key
    const currentValue = event.target.value
    const controlKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Home',
      'End',
    ]

    if (controlKeys.includes(key)) return
    if (event.ctrlKey && ['a', 'c', 'v', 'x'].includes(key.toLowerCase())) return

    if (isDecimalUnit(unit)) {
      if (/^\d$/.test(key)) return
      if (key === ',' && !currentValue.includes(',')) return
    } else {
      if (/^\d$/.test(key)) return
    }
    event.preventDefault()
  }

  function updatePaymentDisplay() {
    if (rawPaymentAmount === 0) {
      paymentAmount = '₺0,00'
    } else {
      paymentAmount = formatTurkishCurrency(rawPaymentAmount / 100)
    }
  }

  // TABS
  let activeTab = 'search'
  function changeTab(tabName) {
    activeTab = tabName
  }

  // ULTRA BASIT ARAMA
  function handleInputChange() {
    if (searchTimeout) clearTimeout(searchTimeout)

    if (searchTerm.length < 2) {
      closeDropdown()
      return
    }

    searchTimeout = setTimeout(() => {
      performSearch()
    }, 300)
  }

  async function performSearch() {
    if (searchTerm.length < 2) return

    // Focus'u koru
    const inputWasFocused = document.activeElement === searchInput

    isSearching = true
    try {
      const results = await dbSearchInventory(searchTerm)
      searchResults = results ? results.slice(0, 5) : []

      if (searchResults.length > 0) {
        openDropdown()
        // Focus'u geri ver
        if (inputWasFocused && searchInput) {
          setTimeout(() => {
            searchInput.focus()
          }, 0)
        }
      } else {
        closeDropdown()
      }
    } catch (error) {
      console.error('Arama hatası:', error)
      closeDropdown()
    }
    isSearching = false
  }

  // Input blur olayını kontrol et - dropdown açıksa focus'u geri ver
  function handleInputBlur(_) {
    // Eğer dropdown açık ve blur dropdown'dan kaynaklanıyorsa focus'u geri ver
    if (showDropdown) {
      setTimeout(() => {
        if (searchInput && !searchInput.matches(':focus')) {
          searchInput.focus()
        }
      }, 10)
    }
  }

  // DROPDOWN PORTAL SYSTEM - BODY'YE EKLER
  let dropdownElement = null
  let inputRect = null

  function openDropdown() {
    closeDropdown() // Önce varsa kapat

    if (!searchInput || searchResults.length === 0) return

    // Input focus'unu koru
    const wasInputFocused = document.activeElement === searchInput

    // Input pozisyonunu al
    inputRect = searchInput.getBoundingClientRect()

    // Dropdown element oluştur
    dropdownElement = document.createElement('div')
    dropdownElement.style.cssText = `
      position: fixed;
      top: ${inputRect.bottom + 4}px;
      left: ${inputRect.left}px;
      width: ${inputRect.width}px;
      background: white;
      border: 2px solid #1e3a8a;
      border-radius: 8px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      z-index: 99999;
      max-height: 300px;
      overflow-y: auto;
    `

    // İçeriği ekle
    dropdownElement.innerHTML = searchResults
      .map(
        (item, index) => `
      <div class="dropdown-item" data-index="${index}" style="
        padding: 1rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        ${index === selectedIndex ? 'background: #f8f9fb; border-left: 4px solid #1e3a8a;' : ''}
      ">
        <div style="font-weight: bold; margin-bottom: 0.5rem;">${item.name}</div>
        <div style="display: flex; gap: 0.5rem; font-size: 0.8rem;">
          <span style="background: #f1f5f9; padding: 0.2rem 0.5rem; border-radius: 4px;">Kod: ${item.inventory_code}</span>
          <span style="background: #1e3a8a; color: white; padding: 0.2rem 0.5rem; border-radius: 4px;">
            ${item.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
          </span>
        </div>
      </div>
    `
      )
      .join('')

    // Click eventleri ekle - mousedown ile focus kaybını engelle
    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      // Mouse down'da focus kaybını engelle
      el.addEventListener('mousedown', e => {
        e.preventDefault() // Input'un blur olmasını engelle
      })

      el.addEventListener('click', e => {
        e.preventDefault()
        selectItem(searchResults[index])
      })
    })

    // Body'ye ekle
    document.body.appendChild(dropdownElement)
    showDropdown = true
    selectedIndex = -1

    // Input focus'unu geri ver
    if (wasInputFocused) {
      setTimeout(() => {
        searchInput?.focus()
      }, 0)
    }
  }

  function closeDropdown() {
    if (dropdownElement) {
      document.body.removeChild(dropdownElement)
      dropdownElement = null
    }
    showDropdown = false
    selectedIndex = -1
  }

  function updateDropdownSelection() {
    if (!dropdownElement) return

    dropdownElement.querySelectorAll('.dropdown-item').forEach((el, index) => {
      if (index === selectedIndex) {
        el.style.background = '#f8f9fb'
        el.style.borderLeft = '4px solid #1e3a8a'
        el.scrollIntoView({ block: 'nearest' })
      } else {
        el.style.background = 'white'
        el.style.borderLeft = 'none'
      }
    })
  }

  function selectItem(item) {
    closeDropdown()
    addItemToSales(item)
    searchTerm = ''
    searchInput?.focus()
  }

  // KLAVYE NAVİGASYONU
  function handleKeyDown(event) {
    // ENTER tuşu için özel kontrol - dropdown açık olmasa da çalışsın
    if (event.key === 'Enter') {
      event.preventDefault()

      // Eğer dropdown açık ve sonuçlar varsa
      if (showDropdown && searchResults.length > 0) {
        if (selectedIndex >= 0) {
          selectItem(searchResults[selectedIndex])
        } else {
          selectItem(searchResults[0])
        }
      }
      // Eğer dropdown kapalı ama arama terimi varsa, arama yap ve ilk sonucu ekle
      else if (searchTerm.trim().length >= 2) {
        performSearchAndAddFirst()
      }
      return
    }

    // Diğer tuşlar için dropdown açık olmalı
    if (!showDropdown || searchResults.length === 0) return

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectedIndex = selectedIndex < searchResults.length - 1 ? selectedIndex + 1 : 0
        updateDropdownSelection()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : searchResults.length - 1
        updateDropdownSelection()
        break
      case 'Escape':
        closeDropdown()
        break
    }
  }

  // ENTER ile arama yap ve ilk sonucu ekle
  async function performSearchAndAddFirst() {
    if (isSearching || !searchTerm.trim()) return

    console.log('performSearchAndAddFirst çalışıyor, searchTerm:', searchTerm.trim())
    isSearching = true
    try {
      const results = await dbSearchInventory(searchTerm.trim())
      console.log('Arama sonuçları:', results)

      if (results && results.length > 0) {
        console.log('İlk sonuç:', results[0])
        console.log('addItemToSales çağrılıyor...')
        addItemToSales(results[0])
        searchTerm = ''
        searchInput?.focus()
      } else {
        showError('Ürün bulunamadı')
      }
    } catch (error) {
      console.error('Arama hatası:', error)
      showError('Arama sırasında hata oluştu')
    } finally {
      isSearching = false
    }
  }

  // CLICK OUTSIDE TO CLOSE
  function handleClickOutside(event) {
    if (
      showDropdown &&
      searchInput &&
      !searchInput.contains(event.target) &&
      dropdownElement &&
      !dropdownElement.contains(event.target)
    ) {
      closeDropdown()
    }
  }

  // BARCODE SCANNER
  let barcodeBuffer = ''
  let barcodeTimeout = null

  function handleGlobalKeyDown(event) {
    const target = event.target
    const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA'

    if (!isInputField && event.key.length === 1) {
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      barcodeBuffer += event.key
      barcodeTimeout = setTimeout(() => {
        if (barcodeBuffer.length >= 3) {
          searchTerm = barcodeBuffer
          performSearch()
        }
        barcodeBuffer = ''
      }, 100)
    }
  }

  // SALES MANAGEMENT
  function addItemToSales(item) {
    console.log('addItemToSales çağrıldı, item:', item)
    console.log('Mevcut salesItems:', salesItems)

    const existingItemIndex = salesItems.findIndex(
      salesItem => salesItem.id === item.id || salesItem.inventory_code === item.inventory_code
    )

    console.log('existingItemIndex:', existingItemIndex)

    if (existingItemIndex !== -1) {
      console.log('Mevcut ürün bulundu, miktarı artırılıyor')
      incrementExistingItem(existingItemIndex, item)
    } else {
      console.log('Yeni ürün ekleniyor')
      addNewItemToSales(item)
    }

    console.log('İşlem sonrası salesItems:', salesItems)
  }

  function incrementExistingItem(existingItemIndex, item) {
    const updatedSalesItems = [...salesItems]
    const existingItem = updatedSalesItems[existingItemIndex]
    const increment = getQuantityStep(existingItem.unit)
    existingItem.quantity = Math.round((existingItem.quantity + increment) * 100) / 100
    existingItem.total = existingItem.quantity * existingItem.price
    salesItems = updatedSalesItems
    showSuccess(
      `${item.name} miktarı artırıldı (${formatQuantity(existingItem.quantity)} ${existingItem.unit})`
    )
  }

  function addNewItemToSales(item) {
    const initialQuantity = getMinimumQuantity(item.unit || 'adet')
    const salesItem = {
      sequenceNo: sequenceNumber++,
      id: item.id,
      name: item.name,
      unit: item.unit || 'adet',
      price: parseFloat(item.price) || 0,
      inventory_code: item.inventory_code,
      barcode: item.barcode || '',
      quantity: initialQuantity,
      total: (parseFloat(item.price) || 0) * initialQuantity,
    }
    salesItems = [...salesItems, salesItem]
    showSuccess(`${item.name} satış listesine eklendi`)
  }

  function removeItem(index) {
    const itemToRemove = salesItems[index]
    salesItems = salesItems.filter((_, i) => i !== index)
    reorderSequenceNumbers(index)
    showSuccess(`${itemToRemove.name} satış listesinden kaldırıldı`)
  }

  function reorderSequenceNumbers(removedIndex) {
    if (removedIndex === 0) {
      salesItems = salesItems.map(item => ({ ...item, sequenceNo: item.sequenceNo - 1 }))
      sequenceNumber = sequenceNumber - 1
    } else {
      salesItems = salesItems.map(item => ({
        ...item,
        sequenceNo: item.sequenceNo > removedIndex + 1 ? item.sequenceNo - 1 : item.sequenceNo,
      }))
      sequenceNumber = sequenceNumber - 1
    }
  }

  function increaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const increment = getQuantityStep(item.unit)
    item.quantity = Math.round((item.quantity + increment) * 100) / 100
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
    showSuccess(`${item.name} miktarı artırıldı (${formatQuantity(item.quantity)} ${item.unit})`)
  }

  function decreaseQuantity(index) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]
    const decrement = getQuantityStep(item.unit)
    const minQuantity = getMinimumQuantity(item.unit)
    const newQuantity = Math.round((item.quantity - decrement) * 100) / 100

    if (newQuantity >= minQuantity) {
      item.quantity = newQuantity
      item.total = item.quantity * item.price
      salesItems = updatedSalesItems
      showSuccess(`${item.name} miktarı azaltıldı (${formatQuantity(item.quantity)} ${item.unit})`)
    } else {
      removeItem(index)
    }
  }

  function handleQuantityChange(index, newQuantity) {
    const updatedSalesItems = [...salesItems]
    const item = updatedSalesItems[index]

    if (!validateQuantityInput(newQuantity, item.unit)) {
      showError(
        `${item.unit} birimi için ${isDecimalUnit(item.unit) ? 'ondalık değer (örn: 1,50)' : 'tam sayı'} giriniz`
      )
      return
    }

    const normalizedQuantity = parseQuantityInput(newQuantity)
    let quantity = parseFloat(normalizedQuantity)
    const minQuantity = getMinimumQuantity(item.unit)
    if (isNaN(quantity) || quantity <= 0) quantity = minQuantity
    quantity = Math.round(quantity * 100) / 100

    if (quantity < minQuantity) {
      removeItem(index)
      return
    }

    item.quantity = quantity
    item.total = item.quantity * item.price
    salesItems = updatedSalesItems
  }

  $: totalAmount = salesItems.reduce((sum, item) => sum + item.total, 0)
  $: remainingAmount = totalAmount - totalPaidAmount
  $: isPaymentComplete = remainingAmount <= 0 && totalAmount > 0

  // PAYMENT METHODS
  function addToAmount(digit) {
    rawPaymentAmount = rawPaymentAmount * 10 + (digit === '.' ? 0 : parseInt(digit))
    updatePaymentDisplay()
  }

  function removeLastDigit() {
    rawPaymentAmount = Math.floor(rawPaymentAmount / 10)
    updatePaymentDisplay()
  }

  function clearAmount() {
    rawPaymentAmount = 0
    updatePaymentDisplay()
  }

  function openCashChangeModal() {
    cashReceived = rawPaymentAmount / 100
    changeAmount = Math.max(0, cashReceived - remainingAmount)
    showCashChangeModal = true
  }

  function closeCashChangeModal() {
    showCashChangeModal = false
    cashReceived = 0
    changeAmount = 0
  }

  function completeCashSale() {
    if (cashReceived < remainingAmount) {
      showError('Alınan nakit tutar yetersiz!')
      return
    }

    const actualPayment = Math.min(cashReceived, remainingAmount)
    const success = addPartialPayment('cash', actualPayment)

    if (success) {
      selectedPaymentMethod = ''
      rawPaymentAmount = 0
      updatePaymentDisplay()
      document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('is-active', 'is-selected')
      })

      if (changeAmount > 0) {
        showSuccess(
          `Nakit ödeme tamamlandı! Para üstü: ${changeAmount.toLocaleString('tr-TR', {
            style: 'currency',
            currency: 'TRY',
          })}`
        )
      } else {
        showSuccess('Nakit ödeme tamamlandı!')
      }

      closeCashChangeModal()
    }
  }

  function selectPaymentMethod(method) {
    selectedPaymentMethod = method
    document.querySelectorAll('.payment-btn').forEach(btn => {
      btn.classList.remove('is-active', 'is-selected')
    })
    const selectedButton = document.querySelector(
      `.payment-btn.${method.toLowerCase().replace(/\s+/g, '-')}`
    )
    if (selectedButton) {
      selectedButton.classList.add('is-active', 'is-selected')
    }

    // Smart payment behavior for card payments
    if (method === 'credit-card' || method === 'meal-card') {
      handleCardPayment(method)
    }
  }

  async function handleCardPayment(method) {
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    if (remainingAmount <= 0) {
      showError('Ödeme zaten tamamlanmış!')
      return
    }

    const currentPaymentAmount = rawPaymentAmount / 100 // Convert from cents to TL

    console.log('💳 Kart ödeme işlemi:', {
      method,
      currentPaymentAmount,
      remainingAmount,
      hasKeypadInput: rawPaymentAmount > 0,
    })

    // If no keypad input, use remaining amount
    const paymentAmount = currentPaymentAmount > 0 ? currentPaymentAmount : remainingAmount

    // Validate payment amount
    if (paymentAmount > remainingAmount) {
      showError(`Ödeme tutarı kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla olamaz!`)
      return
    }

    try {
      showSuccess(
        `${method === 'credit-card' ? 'Kredi kartı' : 'Yemek kartı'} ile ${paymentAmount.toFixed(2)} TL ödeme POS'a gönderiliyor...`
      )

      // Add partial payment first
      const success = addPartialPayment(method, paymentAmount)

      if (success) {
        // Clear payment method selection and keypad
        selectedPaymentMethod = ''
        rawPaymentAmount = 0
        updatePaymentDisplay()
        document.querySelectorAll('.payment-btn').forEach(btn => {
          btn.classList.remove('is-active', 'is-selected')
        })

        // Send to POS immediately for card payments
        console.log("🏧 Kart ödemesi POS'a gönderiliyor:", {
          method,
          amount: paymentAmount,
          partialPayments,
          isComplete: isPaymentComplete,
        })

        await sendRealSaleData()

        // Check if payment is now complete
        if (isPaymentComplete) {
          showSuccess('Kart ödemesi başarılı! Satış tamamlandı!')

          // Clear everything after complete payment
          salesItems = []
          partialPayments = []
          totalPaidAmount = 0
        } else {
          showSuccess(`Kart ödemesi başarılı! Kalan: ${remainingAmount.toFixed(2)} TL`)
        }
      }
    } catch (error) {
      console.error('❌ Kart ödeme hatası:', error)
      showError(`Kart ödeme hatası: ${error.message}`)

      // Remove the failed payment from partial payments
      if (partialPayments.length > 0) {
        const lastPayment = partialPayments[partialPayments.length - 1]
        if (lastPayment.method === method && lastPayment.amount === paymentAmount) {
          partialPayments = partialPayments.slice(0, -1)
          totalPaidAmount -= paymentAmount
          showError('Ödeme başarısız oldu, kart ödemesi geri alındı')
        }
      }
    }
  }

  // PARTIAL PAYMENT FUNCTIONS
  function addPartialPayment(method, amount) {
    if (amount <= 0) {
      showError("Ödeme tutarı 0'dan büyük olmalıdır!")
      return false
    }

    if (amount > remainingAmount) {
      showError(`Kalan tutardan (${remainingAmount.toFixed(2)} TL) fazla ödeme yapılamaz!`)
      return false
    }

    const payment = {
      id: Date.now(),
      method,
      amount,
      timestamp: new Date().toISOString(),
    }

    partialPayments = [...partialPayments, payment]
    totalPaidAmount += amount

    console.log('💰 Kısmi ödeme eklendi:', {
      payment,
      totalPaid: totalPaidAmount,
      remaining: remainingAmount,
      isComplete: isPaymentComplete,
    })

    showSuccess(`${amount.toFixed(2)} TL ${method} ödemesi eklendi`)

    // Clear the payment input
    rawPaymentAmount = 0
    updatePaymentDisplay()

    return true
  }

  function removePartialPayment(paymentId) {
    const payment = partialPayments.find(p => p.id === paymentId)
    if (payment) {
      partialPayments = partialPayments.filter(p => p.id !== paymentId)
      totalPaidAmount -= payment.amount
      showSuccess(`${payment.amount.toFixed(2)} TL ${payment.method} ödemesi kaldırıldı`)
    }
  }

  function clearAllPayments() {
    partialPayments = []
    totalPaidAmount = 0
    showSuccess('Tüm ödemeler temizlendi')
  }

  async function makeRequest(deviceIp, path, method, payload, retryCount = 0) {
    const MAX_RETRIES = 3

    console.log(`[POS] Request attempt ${retryCount + 1}/${MAX_RETRIES + 1}`)

    if (retryCount >= MAX_RETRIES) {
      throw new Error('İstek tekrar denemesi başarısız oldu. Lütfen işlemi tekrar deneyiniz.')
    }

    try {
      console.log(`[POS] Making ${method} request to: https://${deviceIp}:4567/${path}`)
      console.log('[POS] Payload:', JSON.stringify(payload, null, 2))

      const response = await window.electronAPI.https.request({
        hostname: deviceIp,
        port: 4567,
        path,
        method,
        payload,
      })

      console.log('[POS] Response:', response)

      if (response.HasError) {
        console.error('[PAVO] Error response:', response)
        console.log('[PAVO] Error Code:', response.ErrorCode, 'Retry Count:', retryCount)

        if (response.ErrorCode === 73) {
          console.log(
            '[PAVO] Sequence error, retrying with new sequence:',
            response.TransactionHandle.TransactionSequence
          )

          // Update global sequence number
          sequenceNumber = response.TransactionHandle.TransactionSequence
          console.log('📊 Global sequenceNumber updated to:', sequenceNumber)

          // Update payload with new sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          // Retry the request
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else if (response.ErrorCode === 72) {
          console.log(
            '[PAVO] Time sync error, retrying with POS time:',
            response.TransactionHandle.TransactionDate
          )
          console.log('[PAVO] Original time was:', payload.Header?.DateTime)
          console.log(
            '[PAVO] Original sequence was:',
            payload.TransactionHandle?.TransactionSequence
          )

          // Update global sequence number from time sync error too
          if (response.TransactionHandle && response.TransactionHandle.TransactionSequence) {
            sequenceNumber = response.TransactionHandle.TransactionSequence
            console.log('📊 Global sequenceNumber updated from time sync:', sequenceNumber)
          }

          // Update payload with POS time and sequence
          if (payload.TransactionHandle) {
            payload.TransactionHandle.TransactionSequence =
              response.TransactionHandle.TransactionSequence
          }

          if (payload.Header) {
            payload.Header.DateTime = response.TransactionHandle.TransactionDate
          }

          if (payload.Transaction) {
            payload.Transaction.TransactionDate = response.TransactionHandle.TransactionDate
          }

          console.log('[PAVO] Updated payload for retry:', {
            sequence: payload.TransactionHandle?.TransactionSequence,
            headerTime: payload.Header?.DateTime,
            transactionTime: payload.Transaction?.TransactionDate,
          })

          // Retry the request
          console.log('[PAVO] Starting retry with corrected time...')
          return await makeRequest(deviceIp, path, method, payload, retryCount + 1)
        } else {
          console.error('PAVO MakeRequest error:', response)
          throw new Error(
            response.Message || 'İstek gönderildi, ama PAVO bilinmeyen bir hata dönüşü yaptı'
          )
        }
      }

      // Başarılı response'larda da sequence güncelle
      if (
        response &&
        response.TransactionHandle &&
        response.TransactionHandle.TransactionSequence
      ) {
        const newSequence = response.TransactionHandle.TransactionSequence
        if (newSequence !== sequenceNumber) {
          console.log("📊 Başarılı response'tan sequence güncelleniyor:", {
            old: sequenceNumber,
            new: newSequence,
            path,
          })
          sequenceNumber = newSequence
        }
      }

      return response
    } catch (error) {
      console.error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu', error)
      throw new Error('İstek gönderildi, ama cihazın yanıtı alımı sırasında hata oluştu')
    }
  }

  /**
   * Demo satış verisi gönderme fonksiyonu (test amaçlı)
   */
  async function sendDemoSaleData() {
    console.log('🎯 Demo satış verisi gönderiliyor...')

    try {
      // Türkiye saati formatı
      const now = new Date()
      now.setHours(now.getHours() + 3)
      let transactionDate = now.toISOString().split('Z')[0]

      // Limit to 3 digits after decimal point for milliseconds
      const parts = transactionDate.split('.')
      if (parts.length === 2) {
        transactionDate = `${parts[0]}.${parts[1].substring(0, 3)}`
      }

      // TypeScript uyumlu payload yapısı
      const currentSequence = sequenceNumber + 1 // Demo için de sequence'ı 1 artır

      console.log('📊 Demo TransactionSequence kullanılıyor:', {
        current: sequenceNumber,
        sending: currentSequence,
      })

      const demoData = {
        TransactionHandle: {
          SerialNumber: 'PAV600000327',
          TransactionDate: transactionDate,
          TransactionSequence: currentSequence,
          Fingerprint: 'shopigo',
        },
        Sale: {
          RefererApp: 'shopigo',
          RefererAppVersion: '1.0.0',
          OrderNo: `DEMO-${Date.now()}`,
          MainDocumentType: 1, // E_INVOICE
          GrossPrice: 50.0,
          TotalPrice: 50.0,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          SendPhoneNotification: false,
          SendEMailNotification: false,
          NotificationPhone: '',
          DocumentNote: '',
          NotificationEMail: '<EMAIL>',
          ShowCreditCardMenu: false,
          SelectedSlots: ['rf', 'icc', 'manual'],
          EnableAllTerminalsOnRetry: false,
          AllowDismissCardRead: false,
          CardReadTimeout: 30,
          SkipAmountCash: true,
          CancelPaymentLater: true,
          AskCustomer: false,
          SendResponseBeforePrint: false,
          TryAgainOnPaymentFailure: true,
          ReferOtherMediatorsToRetryPayment: true,
          AbandonOptions: {
            IsVoid: true,
            EnableRefundMediatorsOnVoidFailure: true,
          },
          ContinuePaymentWithCardInserted: true,
          HeadUnmaskedCardNumber: 4,
          TailUnmaskedCardNumber: 4,
          ReceiptInformation: {
            ReceiptJsonEnabled: true,
            ReceiptTextEnabled: false,
            ReceiptImageEnabled: false,
            ReceiptWidth: '58mm',
            PrintCustomerReceipt: false,
            PrintMerchantReceipt: false,
            PrintCustomerReceiptCopy: false,
            EnableExchangeRateField: true,
          },
          AddedSaleItems: [
            {
              Name: 'Demo Kahve',
              IsGeneric: true,
              UnitCode: 'C62', // ADET
              TaxGroupCode: 'KDV18',
              ItemQuantity: 2,
              UnitPriceAmount: 15.0,
              GrossPriceAmount: 30.0, // Total for this item (quantity × unit price)
              TotalPriceAmount: 30.0,
              ReservedText: 'DEMO-COFFEE-001',
            },
            {
              Name: 'Demo Sandviç',
              IsGeneric: true,
              UnitCode: 'C62', // ADET
              TaxGroupCode: 'KDV18',
              ItemQuantity: 1,
              UnitPriceAmount: 20.0,
              GrossPriceAmount: 20.0, // Total for this item (quantity × unit price)
              TotalPriceAmount: 20.0,
              ReservedText: 'DEMO-SANDWICH-002',
            },
          ],
          PaymentInformations: [
            {
              Mediator: 2, // Kredi kartı
              Amount: 50.0,
              CurrencyCode: 'TRY',
              ExchangeRate: 1,
              ExternalReferenceText: `demo-ref-${Date.now()}`,
            },
          ],
          AllowedPaymentMediators: [{ Mediator: 2 }, { Mediator: 1 }],
          CustomerParty: {
            CustomerType: 1,
            FirstName: 'Demo',
            MiddleName: 'demo',
            FamilyName: 'Müşteri',
            CompanyName: 'demo',
            TaxOfficeCode: '',
            TaxNumber: '11111111111',
            Phone: '',
            EMail: '',
            Country: 'Türkiye',
            City: 'Istanbul',
            District: '',
            Neighborhood: '',
            Address: '',
          },
          AdditionalInfo: [
            {
              Key: 'ShopigoFurpa',
              Value: 'Demo Satış',
              Print: true,
            },
          ],
        },
      }

      console.log('📤 Demo verisi gönderiliyor:', demoData)
      showSuccess('Demo satış gönderiliyor...')

      const response = await makeRequest('************', 'CompleteSale', 'POST', demoData)

      // Demo satış başarılıysa sequence'ı güncelle
      sequenceNumber = currentSequence
      console.log('📊 Demo TransactionSequence güncellendi:', sequenceNumber)

      console.log('✅ Demo satış başarılı:', response)
      showSuccess('Demo satış başarıyla tamamlandı!')

      return response
    } catch (error) {
      console.error('❌ Demo satış hatası:', error)
      showError(`Demo satış hatası: ${error.message}`)
      throw error
    }
  }

  /**
   * Process sale using exact same format as TypeScript version
   */
  async function processSale(saleData) {
    const deviceIp = saleData.deviceIp || '************'
    const serialNumber = saleData.serialNumber || 'PAV600000327'

    try {
      // Create transaction date (same format as TypeScript)
      const now = new Date()
      now.setHours(now.getHours() + 3) // Add 3 hours like in TypeScript
      const transactionDate = now.toISOString().split('Z')[0] // Remove Z

      // Build the exact same payload structure as TypeScript
      const transactionHandle = {
        SerialNumber: serialNumber,
        TransactionDate: transactionDate,
        TransactionSequence: saleData.transactionSequence || 2,
        Fingerprint: 'shopigo',
      }

      const salePayload = {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: `ORDER1-${Date.now()}`, //saleData.orderNo ||
        MainDocumentType: 1, // DocumentType.E_INVOICE
        GrossPrice: saleData.totalPrice,
        TotalPrice: saleData.totalPrice,
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        SendPhoneNotification: false,
        SendEMailNotification: false,
        NotificationPhone: '',
        DocumentNote: '',
        Reserved03: '',
        NotificationEMail: saleData.email || '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: true, // Same as TypeScript
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        ReceiptInformation: {
          ReceiptJsonEnabled: true,
          ReceiptTextEnabled: false,
          ReceiptImageEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: false, // Same as TypeScript
          PrintMerchantReceipt: false, // Same as TypeScript
          PrintCustomerReceiptCopy: false,
          EnableExchangeRateField: true,
        },
        AddedSaleItems: saleData.items.map(item => ({
          Name: item.name,
          IsGeneric: true, // Same as TypeScript
          UnitCode: item.unitCode || 'C62', // ADET
          TaxGroupCode: item.taxGroupCode || 'KDV10',
          ItemQuantity: item.quantity,
          UnitPriceAmount: item.unitPrice || item.price,
          GrossPriceAmount: item.grossPrice || item.unitPrice || item.price,
          TotalPriceAmount: item.totalPrice || item.quantity * (item.unitPrice || item.price),
          ReservedText: item.reservedText || `ITEM-${item.id || Date.now()}`,
          ...(item.discount &&
            item.discount > 0 && {
              PriceEffect: {
                Type: 1, // LineDiscount
                Amount: item.discount,
              },
            }),
        })),
        PaymentInformations: saleData.payments.map(payment => ({
          Mediator: payment.mediator || (payment.method === 'CASH' ? 1 : 2),
          Amount: payment.amount,
          CurrencyCode: 'TRY',
          ExchangeRate: 1,
          ExternalReferenceText: payment.externalReference || `ref-${Date.now()}`,
        })),
        AllowedPaymentMediators: [
          { Mediator: 2 }, // Card
          { Mediator: 1 }, // Cash
        ],
        CustomerParty: saleData.customer || {
          CustomerType: 1,
          FirstName: 'Demo',
          MiddleName: 'demo',
          FamilyName: 'Customer',
          CompanyName: 'demoe',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Istanbul',
          District: 'Demo',
          Neighborhood: '',
          Address: 'bursa',
        },
        AdditionalInfo: [
          {
            Key: 'Test',
            Value: 'JavaScript Demo',
            Print: true,
          },
        ],
        ...(saleData.discount &&
          saleData.discount > 0 && {
            PriceEffect: {
              Type: 2, // SaleDiscount
              Amount: saleData.discount,
            },
          }),
      }

      const requestPayload = {
        TransactionHandle: transactionHandle,
        Sale: salePayload,
      }

      console.log('[POS] Processing sale with payload:', JSON.stringify(requestPayload, null, 2))

      // Use makeRequest function (same as TypeScript)
      const response = await makeRequest(deviceIp, 'CompleteSale', 'POST', requestPayload)

      if (response.HasError) {
        throw new Error(response.Message)
      }

      const receipt = response.Data

      // Clean up receipt data (same as TypeScript)
      if (receipt.MerchantReceiptJson) {
        delete receipt.MerchantReceiptJson
      }

      if (receipt.CustomerReceiptJson) {
        try {
          const receiptData = JSON.parse(receipt.CustomerReceiptJson)
          receipt.CustomerReceiptJson = {
            ...receiptData,
            customerReceipt1: receiptData.customerReceipt1?.filter(item => item.type !== 'image'),
          }
        } catch (e) {
          console.log('Receipt JSON parse error:', e)
        }
      }

      // Status handling (same as TypeScript)
      const statusMessages = {
        1: 'Satış askıya alındı',
        2: 'Ödeme bekleniyor',
        3: 'Doküman oluşturuluyor',
        4: 'Doküman bekleniyor',
        5: 'Doküman oluşturuldu',
        6: 'Satış başarıyla tamamlandı',
        7: 'Satıştan vazgeçildi',
        8: 'Doküman oluşturulamadı',
        9: 'İşlem imzalanıyor',
        10: 'İmzalama işlemi başarısız oldu',
        11: 'İşlem iptal edildi',
        12: 'Ödemeler iptal ediliyor',
        13: 'Ödemeler iptal edildi',
        14: 'Ödeme iptali gerçekleştirilemedi',
        15: 'Doküman iptal ediliyor',
        16: 'Doküman iptali gerçekleştirilemedi',
        17: 'Doküman iptali bekleniyor',
        18: 'İşlem imzalandı',
        19: 'ERP işlemleri devam ediyor',
        20: 'Doküman onaylandı',
        21: 'Doküman onaylanmadı',
        22: 'İnceleme bekleniyor',
        23: 'Tamamlanmamış satış',
        99: 'Ödeme kaydedici sorunu var',
      }

      const statusId = receipt.StatusId || 99
      const statusMessage = statusMessages[statusId] || 'Ödeme kaydedici sorunu var.'

      return {
        success: [5, 6, 18, 20].includes(statusId), // Successful statuses
        message: statusMessage,
        data: {
          payload: receipt,
          processor_id: 1, // Mock processor ID
        },
      }
    } catch (error) {
      console.error('[POS] Sale processing error:', error)
      return {
        success: false,
        message: error.message || 'Satış işlemi sırasında bir hata oluştu',
        data: null,
      }
    }
  }

  /**
   * Pairing function using https module
   */
  async function pairDevice(deviceConfig) {
    console.log('[DEMO] Starting device pairing...')
    const now = new Date()
    now.setHours(now.getHours() + 3)
    const transactionDate = now.toISOString().split('Z')[0]

    try {
      const transactionHandle = {
        TransactionHandle: {
          SerialNumber: deviceConfig.serialNumber,
          TransactionDate: transactionDate,
          TransactionSequence: 1,
          Fingerprint: 'shopigo',
        },
      }

      console.log('[DEMO] Pairing with payload:', JSON.stringify(transactionHandle, null, 2))

      const response = await makeRequest(
        deviceConfig.ipAddress,
        'Pairing',
        'POST',
        transactionHandle
      )

      console.log('[DEMO] Device paired successfully:', response)
      console.log('[DEMO] Response structure analysis:', {
        hasResponse: !!response,
        responseKeys: response ? Object.keys(response) : null,
        hasTransactionHandle: !!(response && response.TransactionHandle),
        transactionHandleKeys:
          response && response.TransactionHandle ? Object.keys(response.TransactionHandle) : null,
        transactionSequence:
          response && response.TransactionHandle
            ? response.TransactionHandle.TransactionSequence
            : 'NOT_FOUND',
        fullResponse: JSON.stringify(response, null, 2),
      })

      // POS'tan gelen TransactionSequence'ı al ve sakla
      if (
        response &&
        response.TransactionHandle &&
        response.TransactionHandle.TransactionSequence
      ) {
        sequenceNumber = response.TransactionHandle.TransactionSequence
        console.log("📊 POS'tan gelen TransactionSequence:", sequenceNumber)
      } else {
        // Fallback: Pairing başarılıysa 2'den başla
        sequenceNumber = 2
        console.log('📊 Fallback TransactionSequence:', sequenceNumber)
        console.log('📊 Fallback nedeni - Response analizi:', {
          hasResponse: !!response,
          hasTransactionHandle: !!(response && response.TransactionHandle),
          hasSequence: !!(
            response &&
            response.TransactionHandle &&
            response.TransactionHandle.TransactionSequence
          ),
        })
      }

      return {
        success: true,
        message: 'Device paired successfully',
        device: {
          id: Date.now(), // Mock ID
          serialNumber: deviceConfig.serialNumber,
          ipAddress: deviceConfig.ipAddress,
          name: deviceConfig.name,
          isActive: true,
          isPaired: true,
          isDefault: true,
          transactionSequence: sequenceNumber,
        },
      }
    } catch (error) {
      console.error('[DEMO] Pairing error:', error)
      return {
        success: false,
        message: error.message || 'Pairing failed',
        device: null,
      }
    }
  }

  /**
   * Send real sale data to POS terminal using current cart items
   */
  async function sendRealSaleData() {
    if (salesItems.length === 0) {
      throw new Error('Satış listesi boş!')
    }
    pairDevice()

    // Calculate dynamic payment amount based on keypad and remaining amount
    const keypadAmount = rawPaymentAmount / 100 // Convert from cents to TL
    const dynamicPaymentAmount =
      keypadAmount > 0 && keypadAmount <= remainingAmount ? keypadAmount : remainingAmount

    console.log('💰 Dinamik ödeme tutarı hesaplaması:', {
      keypadAmount,
      remainingAmount,
      dynamicPaymentAmount,
      hasKeypadInput: keypadAmount > 0,
      useKeypad: keypadAmount > 0 && keypadAmount <= remainingAmount,
    })

    // Generate unique order number
    const orderNo = `ORDER-${Date.now()}`

    // Generate the current date and time in Turkey timezone (UTC+3)
    const now = new Date()
    now.setHours(now.getHours() + 3)
    let transactionDate = now.toISOString().split('Z')[0]

    // Limit to 3 digits after decimal point for milliseconds
    const parts = transactionDate.split('.')
    if (parts.length === 2) {
      transactionDate = `${parts[0]}.${parts[1].substring(0, 3)}`
    }

    console.log('🕐 Transaction time:', {
      utc: new Date().toISOString(),
      turkey: transactionDate,
      timezone: 'UTC+3',
      format: 'YYYY-MM-DDTHH:mm:ss.fff',
    })

    // Convert salesItems to TypeScript format (AddedSaleItems)
    const addedSaleItems = salesItems.map((item, index) => {
      const quantity = parseFloat(item.quantity || 1)
      const total = parseFloat(item.total || 0)

      // Use 'price' field from salesItems (not 'unitPrice')
      let unitPrice = parseFloat(item.price || 0)

      // Calculate unit price from total if price is missing or 0
      if (unitPrice <= 0 && total > 0 && quantity > 0) {
        unitPrice = total / quantity
      }

      // Ensure minimum unit price (POS requirement)
      if (unitPrice <= 0) {
        unitPrice = 0.01 // Minimum 1 kuruş
      }

      // CRITICAL: Round to 2 decimal places to avoid floating point issues
      unitPrice = Math.round(unitPrice * 100) / 100
      const calculatedTotal = Math.round(quantity * unitPrice * 100) / 100

      // ALTERNATIVE APPROACH: Try different GrossPriceAmount strategies
      // Strategy 1: GrossPrice = TotalPrice (some POS systems expect this)
      // Strategy 2: GrossPrice = UnitPrice (traditional approach)

      // TOGGLE STRATEGY HERE if needed
      const useStrategy1 = true // Change to false to try Strategy 2
      const grossPrice = useStrategy1 ? calculatedTotal : unitPrice

      console.log(`📦 Ürün ${index + 1}:`, {
        name: item.name,
        quantity,
        unitPrice,
        grossPrice,
        calculatedTotal,
        originalTotal: total,
        originalPrice: item.price,
        strategy: useStrategy1 ? 'Strategy1: GrossPrice=Total' : 'Strategy2: GrossPrice=UnitPrice',
        mathCheck: `${quantity} × ${unitPrice} = ${calculatedTotal}`,
        grossCheck: `GrossPrice = ${grossPrice}`,
        validation: useStrategy1
          ? calculatedTotal === grossPrice
            ? '✅'
            : '❌'
          : unitPrice === grossPrice
            ? '✅'
            : '❌',
        posValidation: `Q(${quantity}) × U(${unitPrice}) = T(${calculatedTotal}), G(${grossPrice})`,
      })

      return {
        Name: item.name || `Ürün ${index + 1}`,
        IsGeneric: true,
        UnitCode: 'C62', // ADET
        TaxGroupCode: 'KDV18',
        ItemQuantity: quantity,
        UnitPriceAmount: unitPrice,
        GrossPriceAmount: grossPrice, // Set to calculated total for POS validation
        TotalPriceAmount: calculatedTotal, // Always quantity × unitPrice
        ReservedText: item.inventory_code || item.barcode || item.id || `ITEM-${index + 1}`,
      }
    })

    // Calculate accurate total from AddedSaleItems first
    const calculatedTotalAmount = addedSaleItems.reduce(
      (sum, item) => sum + item.TotalPriceAmount,
      0
    )

    // If payment is partial, scale AddedSaleItems proportionally
    const isPartialPayment = dynamicPaymentAmount < calculatedTotalAmount
    const scaleFactor = isPartialPayment ? dynamicPaymentAmount / calculatedTotalAmount : 1

    console.log('📊 AddedSaleItems ölçeklendirme:', {
      calculatedTotalAmount,
      dynamicPaymentAmount,
      isPartialPayment,
      scaleFactor,
    })

    // Scale AddedSaleItems if partial payment
    const scaledSaleItems = addedSaleItems.map(item => {
      if (!isPartialPayment) return item

      // For partial payment, scale UNIT PRICE not quantity
      // This ensures: quantity × scaledUnitPrice = scaledTotalPrice
      const scaledUnitPrice = Math.round(item.UnitPriceAmount * scaleFactor * 100) / 100
      const scaledTotalPrice = Math.round(item.ItemQuantity * scaledUnitPrice * 100) / 100
      const scaledGrossPrice = scaledTotalPrice // For POS validation

      console.log(`📊 Ürün ölçeklendirme: ${item.Name}`, {
        originalUnitPrice: item.UnitPriceAmount,
        scaledUnitPrice,
        quantity: item.ItemQuantity,
        originalTotal: item.TotalPriceAmount,
        scaledTotal: scaledTotalPrice,
        mathCheck: `${item.ItemQuantity} × ${scaledUnitPrice} = ${scaledTotalPrice}`,
      })

      return {
        ...item,
        UnitPriceAmount: scaledUnitPrice,
        GrossPriceAmount: scaledGrossPrice,
        TotalPriceAmount: scaledTotalPrice,
      }
    })

    // Verify scaled total matches dynamic payment amount
    const scaledTotal = scaledSaleItems.reduce((sum, item) => sum + item.TotalPriceAmount, 0)

    console.log('📊 Ölçeklendirme doğrulama:', {
      originalTotal: calculatedTotalAmount,
      scaledTotal,
      dynamicPaymentAmount,
      difference: Math.abs(scaledTotal - dynamicPaymentAmount),
    })

    // Convert payments to TypeScript format (PaymentInformations) - use dynamic payment amount
    const paymentInformations = partialPayments.map((payment, index) => {
      const mediatorMap = {
        cash: 1,
        'credit-card': 2,
        'meal-card': 3,
      }

      // For the last payment (current card payment), use dynamic amount
      const isLastPayment = index === partialPayments.length - 1
      const paymentAmount = isLastPayment ? dynamicPaymentAmount : payment.amount

      return {
        Mediator: mediatorMap[payment.method] || 2,
        Amount: paymentAmount,
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        ExternalReferenceText: `ref-${payment.id}`,
      }
    })

    console.log('💳 Ödeme bilgileri güncellemesi:', {
      partialPayments,
      paymentInformations,
      dynamicPaymentAmount,
      totalPaid: totalPaidAmount,
      calculatedTotal: calculatedTotalAmount,
    })

    console.log('💰 Tutar karşılaştırması:', {
      originalTotal: totalAmount,
      calculatedTotal: calculatedTotalAmount,
      difference: Math.abs(totalAmount - calculatedTotalAmount),
      usingCalculatedTotal: calculatedTotalAmount,
    })

    // TypeScript uyumlu payload yapısı
    const currentSequence = sequenceNumber + 1 // Mevcut sequence'ı 1 artır

    console.log('📊 TransactionSequence kullanılıyor:', {
      current: sequenceNumber,
      sending: currentSequence,
    })

    const data = {
      TransactionHandle: {
        SerialNumber: 'PAV600000327',
        TransactionDate: transactionDate,
        TransactionSequence: currentSequence,
        Fingerprint: 'shopigo',
      },
      Sale: {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: orderNo,
        MainDocumentType: 1, // E_INVOICE
        GrossPrice: dynamicPaymentAmount, // Use dynamic payment amount
        TotalPrice: dynamicPaymentAmount, // Use dynamic payment amount
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        SendPhoneNotification: false,
        SendEMailNotification: false,
        NotificationPhone: '',
        DocumentNote: '',
        NotificationEMail: '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: true,
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        ReceiptInformation: {
          ReceiptJsonEnabled: true,
          ReceiptTextEnabled: false,
          ReceiptImageEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: false,
          PrintMerchantReceipt: false,
          PrintCustomerReceiptCopy: false,
          EnableExchangeRateField: true,
        },
        AddedSaleItems: scaledSaleItems, // Use scaled items for partial payments
        PaymentInformations: paymentInformations,
        AllowedPaymentMediators: [{ Mediator: 2 }, { Mediator: 1 }],
        CustomerParty: {
          CustomerType: 1,
          FirstName: 'Müşteri',
          MiddleName: 'demo',
          FamilyName: 'demo',
          CompanyName: 'demo',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '5417818194',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Istanbul',
          District: '',
          Neighborhood: '',
          Address: '',
        },
        AdditionalInfo: [
          {
            Key: 'ShopigoFurpa',
            Value: 'Gerçek Satış',
            Print: true,
          },
        ],
      },
    }

    try {
      console.log('📤 Gerçek satış verileri gönderiliyor...', {
        items: salesItems,
        total: totalAmount,
        paymentMethod: selectedPaymentMethod,
        posData: data,
      })
      const response = await makeRequest('************', 'CompleteSale', 'POST', data)

      // Satış başarılıysa sequence'ı güncelle
      sequenceNumber = currentSequence
      console.log('📊 TransactionSequence güncellendi:', sequenceNumber)

      console.log('✅ Gerçek satış başarılı:', response)
      return response
    } catch (error) {
      console.error('❌ Gerçek satış hatası:', error)
      throw error
    }
  }

  /**
   * Send demo sale data (for testing)
   */
  async function sendCompleteSaleData() {
    // Generate the current date and time for TransactionDate in Turkey timezone
    const now = new Date()
    now.setHours(now.getHours() + 3)
    const transactionDate = now.toISOString().split('Z')[0]

    const data = {
      TransactionHandle: {
        SerialNumber: 'PAV600000327',
        TransactionDate: transactionDate,
        TransactionSequence: 2,
        Fingerprint: 'shopigo',
      },
      Sale: {
        RefererApp: 'shopigo',
        RefererAppVersion: '1.0.0',
        OrderNo: '0000000000ABC0006',
        MainDocumentType: 1,
        GrossPrice: 20,
        TotalPrice: 20,
        CurrencyCode: 'TRY',
        ExchangeRate: 1,
        PriceEffect: {
          Type: 2,
          Rate: 10,
          Amount: null,
        },
        SendPhoneNotification: false,
        SendEMailNotification: true,
        NotificationPhone: '',
        DocumentNote: '',
        Reserved03: '',
        NotificationEMail: '<EMAIL>',
        ShowCreditCardMenu: false,
        SelectedSlots: ['rf', 'icc', 'manual'],
        EnableAllTerminalsOnRetry: false,
        AllowDismissCardRead: false,
        CardReadTimeout: 30,
        SkipAmountCash: false,
        CancelPaymentLater: true,
        AskCustomer: false,
        SendResponseBeforePrint: false,
        TryAgainOnPaymentFailure: true,
        ReferOtherMediatorsToRetryPayment: true,
        AbandonOptions: {
          IsVoid: true,
          EnableRefundMediatorsOnVoidFailure: true,
        },
        ContinuePaymentWithCardInserted: true,
        HeadUnmaskedCardNumber: 4,
        TailUnmaskedCardNumber: 4,
        AddedSaleItems: [
          {
            Name: 'Gofret',
            IsGeneric: false,
            UnitCode: 'KGM',
            TaxGroupCode: 'KDV10',
            ItemQuantity: 1,
            UnitPriceAmount: 20,
            GrossPriceAmount: 20,
            TotalPriceAmount: 20,
            ReservedText: 'TEST0001',
            PriceEffect: {
              Type: 1,
              Rate: 10,
              Amount: null,
            },
          },
        ],
        PaymentInformations: [
          {
            Mediator: 2,
            Amount: 16.2,
            CurrencyCode: 'TRY',
            ExchangeRate: 1,
            ExternalReferenceText: 'xrkgrtkvr1234',
          },
        ],
        AllowedPaymentMediators: [
          {
            Mediator: 2,
          },
          {
            Mediator: 1,
          },
        ],
        ReceiptInformation: {
          ReceiptImageEnabled: true,
          ReceiptJsonEnabled: false,
          ReceiptTextEnabled: false,
          ReceiptWidth: '58mm',
          PrintCustomerReceipt: true,
          PrintCustomerReceiptCopy: true,
          PrintMerchantReceipt: true,
          EnableExchangeRateField: true,
        },
        CustomerParty: {
          CustomerType: 1,
          FirstName: 'John',
          MiddleName: 'sdasds',
          FamilyName: 'Doe',
          CompanyName: '',
          TaxOfficeCode: '',
          TaxNumber: '11111111111',
          Phone: '5417818194',
          EMail: '<EMAIL>',
          Country: 'Türkiye',
          City: 'Ankara',
          District: 'Çankaya',
          Neighborhood: '',
          Address: 'bursa bursa',
        },
        AdditionalInfo: [
          {
            Key: 'Test',
            Value: 'Test',
            Print: true,
          },
        ],
        TopPrintableItems: [
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'Ücretsiz Ürünler',
            fontWeight: 'bold',
          },
          {
            type: 'dLine',
            dashWidth: 4,
          },
          {
            type: 'dSpace',
            height: 20,
          },
        ],
        BottomPrintableItems: [
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'Ücretsiz Ürünler',
            fontWeight: 'bold',
          },
          {
            type: 'dLine',
            dashWidth: 4,
          },
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Ketçap',
                offset: 0,
                rightMargin: 134,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Mayonez',
                offset: 0,
                rightMargin: 134,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dList',
            children: [
              {
                type: 'dText',
                alignment: 'left',
                text: '1 Adet',
                offset: 0,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: 'Kova Boy Patates',
                offset: 0,
                leftMargin: 200,
              },
              {
                type: 'dText',
                alignment: 'right',
                text: '0 TL',
              },
            ],
          },
          {
            type: 'dSpace',
            height: 20,
          },
          {
            type: 'dLine',
            dashWidth: 15,
          },
          {
            type: 'dText',
            alignment: 'center',
            fontSize: 24,
            text: 'TEK SEFERDE YAPACAĞINIZ 500₺ VE ÜZERİ ALIŞVERİŞLERDE 50₺İNDİRİM SİZLERİ BEKLİYOR',
          },
        ],
      },
    }

    try {
      const response = await makeRequest('************', 'CompleteSale', 'POST', data)
      console.log('Success:', response)
      return response
    } catch (error) {
      console.error('Error sending CompleteSale data:', error)
      throw error
    }
  }

  /**
   * Test functions
   */
  async function testPairing() {
    console.log('=== DEMO PAIRING TEST ===')

    const deviceConfig = {
      serialNumber: 'PAV600000327',
      ipAddress: '************',
      name: 'shopigo',
    }

    const result = await pairDevice(deviceConfig)

    if (result.success) {
      console.log('✅ Pairing successful!')
      console.log('Device info:', result.device)
    } else {
      console.log('❌ Pairing failed:', result.message)
    }

    return result
  }

  /**
   * Test device pairing with detailed logging
   */
  async function testDevicePairing() {
    console.log('🔗 Test POS bağlantısı başlatılıyor...')
    showSuccess('POS cihazı ile bağlantı kuruluyor...')

    try {
      const deviceConfig = {
        serialNumber: 'PAV600000327',
        ipAddress: '************',
        name: 'Test POS Terminal',
      }

      const result = await pairDevice(deviceConfig)

      if (result.success) {
        console.log('✅ POS bağlantısı başarılı!')
        showSuccess(`POS bağlantısı başarılı! Sequence: ${sequenceNumber}`)
      } else {
        console.log('❌ POS bağlantısı başarısız:', result.message)
        showError(`POS bağlantısı başarısız: ${result.message}`)
      }

      return result
    } catch (error) {
      console.error('❌ POS bağlantı hatası:', error)
      showError(`POS bağlantı hatası: ${error.message}`)
      throw error
    }
  }

  function payRemainingAmount() {
    if (remainingAmount <= 0) {
      showError('Kalan tutar bulunmuyor!')
      return
    }

    if (!selectedPaymentMethod) {
      showError('Lütfen ödeme yöntemi seçiniz!')
      return
    }

    console.log('💰 Kalan tutar ödeme:', {
      remainingAmount,
      selectedPaymentMethod,
    })

    // Set keypad to remaining amount
    rawPaymentAmount = Math.round(remainingAmount * 100) // Convert to cents
    updatePaymentDisplay()

    if (selectedPaymentMethod === 'cash') {
      // For cash, just update keypad - user will click "Satışı Tamamla"
      showSuccess(`Kalan tutar (${remainingAmount.toFixed(2)} TL) keypad'e yüklendi`)
    } else if (selectedPaymentMethod === 'credit-card' || selectedPaymentMethod === 'meal-card') {
      // For card payments, immediately process
      handleCardPayment(selectedPaymentMethod)
    }
  }

  // Helper function to save sale to database
  async function saveSaleToDatabase() {
    try {
      // Get current user from store
      let user = null
      currentUser.subscribe(value => {
        user = value
      })()

      if (!user) {
        throw new Error('Kullanıcı bilgisi bulunamadı')
      }

      // Prepare sale data for database
      const saleData = {
        employeeUuid: user.uuid,
        workstationId: 'ws-001', // Default workstation
        customerId: null,
        discount: 0,
        originalPrice: totalAmount,
        totalPrice: totalAmount,
        items: salesItems.map(item => ({
          inventory_code: item.inventory_code,
          quantity: item.quantity,
          unit_price: item.price,
          total_price: item.total,
          weight: isDecimalUnit(item.unit) ? item.quantity : 0,
          weight_unit: isDecimalUnit(item.unit) ? item.unit : null,
          discount: 0,
        })),
        payments: partialPayments.map(payment => ({
          method: payment.method,
          amount: payment.amount,
        })),
      }

      console.log('💾 Veritabanına kaydedilecek satış verisi:', saleData)

      // Save to database
      const result = await saveSaleTransaction(saleData)

      if (result.success) {
        console.log('✅ Satış veritabanına kaydedildi:', result)
        showSuccess(`Satış kaydedildi! Fiş No: ${result.receiptNumber}`)
        return result
      } else {
        throw new Error(result.message || 'Satış kaydedilemedi')
      }
    } catch (error) {
      console.error('❌ Satış kaydetme hatası:', error)
      showError(`Satış kaydetme hatası: ${error.message}`)
      throw error
    }
  }

  async function completeSale() {
    if (salesItems.length === 0) {
      showError('Satış listesi boş!')
      return
    }

    // Handle nakit (cash) payment with keypad input
    if (rawPaymentAmount > 0 && selectedPaymentMethod === 'cash') {
      const paymentAmount = rawPaymentAmount / 100 // Convert from cents to TL

      openCashChangeModal()
      return
    }

    // Auto-complete sale if payment is complete
    if (isPaymentComplete) {
      const usedPaymentMethods = [...new Set(partialPayments.map(p => p.method))]
      const hasCardPayment = usedPaymentMethods.some(
        method => method === 'credit-card' || method === 'meal-card'
      )
      const hasCashPayment = usedPaymentMethods.includes('cash')

      console.log('✅ Ödeme tamamlandı, satış bitirilyor:', {
        totalPaid: totalPaidAmount,
        hasCardPayment,
        hasCashPayment,
      })

      // If only cash payments, don't send to POS but save to database
      if (hasCashPayment && !hasCardPayment) {
        console.log('💰 SADECE NAKİT ÖDEME - Veritabanına kaydet')

        try {
          await saveSaleToDatabase()
          showSuccess('Nakit ödeme kaydedildi!')
        } catch (error) {
          showError(`Satış kaydetme hatası: ${error.message}`)
          return
        }
      } else if (hasCardPayment) {
        console.log("🏧 KART ÖDEMESİ MEVCUT - Kart ödemeleri zaten POS'a gönderildi")

        try {
          await saveSaleToDatabase()
          showSuccess('Satış tamamlandı! Tüm ödemeler kaydedildi.')
        } catch (error) {
          showError(`Satış kaydetme hatası: ${error.message}`)
          return
        }
      }

      // Clear everything after successful transaction
      salesItems = []
      partialPayments = []
      totalPaidAmount = 0
      selectedPaymentMethod = ''
      rawPaymentAmount = 0
      updatePaymentDisplay()

      return
    }

    // If no keypad input and no completed payment, show remaining amount
    if (rawPaymentAmount === 0 && !isPaymentComplete) {
      showError(`Ödeme tamamlanmadı! Kalan tutar: ${remainingAmount.toFixed(2)} TL`)
      return
    }

    // Determine payment methods used
    const usedPaymentMethods = [...new Set(partialPayments.map(p => p.method))]
    const hasCardPayment = usedPaymentMethods.some(
      method => method === 'credit-card' || method === 'meal-card'
    )
    const hasCashPayment = usedPaymentMethods.includes('cash')

    try {
      console.log('💳 Satış işlemi başlatılıyor...', {
        items: salesItems.length,
        total: totalAmount,
        partialPayments,
        totalPaid: totalPaidAmount,
        usedPaymentMethods,
        hasCardPayment,
        hasCashPayment,
      })

      // If only cash payments, don't send to POS but save to database
      if (hasCashPayment && !hasCardPayment) {
        console.log('💰 SADECE NAKİT ÖDEME - Veritabanına kaydet:', {
          payments: partialPayments,
          totalAmount,
          totalPaid: totalPaidAmount,
        })

        try {
          await saveSaleToDatabase()
          showSuccess('Nakit ödeme kaydedildi!')
        } catch (error) {
          showError(`Satış kaydetme hatası: ${error.message}`)
          return
        }

        // Clear everything after cash payment
        salesItems = []
        partialPayments = []
        totalPaidAmount = 0
        selectedPaymentMethod = ''
        rawPaymentAmount = 0
        updatePaymentDisplay()

        return
      }

      // If card payments exist, send to POS
      if (hasCardPayment) {
        console.log('🏧 KART ÖDEMESİ MEVCUT - POS cihazına gönderiliyor:', {
          partialPayments,
          willSendToPOS: true,
        })

        // Send real sale data to POS terminal
        await sendRealSaleData()

        // Save to database after successful POS transaction
        try {
          await saveSaleToDatabase()
          showSuccess(`Satış POS'a gönderildi ve kaydedildi! Toplam: ${totalAmount.toFixed(2)} TL`)
        } catch (error) {
          showError(`POS başarılı ama veritabanı hatası: ${error.message}`)
        }

        // Clear everything after successful POS transaction
        salesItems = []
        partialPayments = []
        totalPaidAmount = 0
        selectedPaymentMethod = ''
        rawPaymentAmount = 0
        updatePaymentDisplay()
      } else {
        // This shouldn't happen, but just in case
        showError('Geçersiz ödeme durumu!')
      }
    } catch (error) {
      console.error('❌ Satış işlemi hatası:', error)
      showError(`Satış işlemi hatası: ${error.message}`)
    }
  }

  // INITIALIZE
  onMount(() => {
    searchInput?.focus()
    updatePaymentDisplay()

    // Event listeners
    document.addEventListener('keydown', handleGlobalKeyDown)
    document.addEventListener('click', handleClickOutside)

    return () => {
      if (searchTimeout) clearTimeout(searchTimeout)
      if (barcodeTimeout) clearTimeout(barcodeTimeout)
      closeDropdown()
      document.removeEventListener('keydown', handleGlobalKeyDown)
      document.removeEventListener('click', handleClickOutside)
    }
  })
</script>

<div class="sales-page">
  <div class="main-layout">
    <!-- Left Panel: Sales Screen (25%) -->
    <div class="sales-panel">
      <!-- Tab Section -->
      <div class="tab-section">
        <div class="tabs is-boxed">
          <ul>
            <li class={activeTab === 'search' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('search')}
                class="tab-button"
                aria-label="Ürün Ara"
              >
                <span class="icon is-small"><i class="fas fa-search"></i></span>
                <span>Ürün Ara</span>
              </button>
            </li>
            <li class={activeTab === 'food' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('food')}
                class="tab-button"
                aria-label="Gıda"
              >
                <span>Gıda</span>
              </button>
            </li>
            <li class={activeTab === 'grocery' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('grocery')}
                class="tab-button"
                aria-label="Manav"
              >
                <span>Manav</span>
              </button>
            </li>
            <li class={activeTab === 'cleaning' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('cleaning')}
                class="tab-button"
                aria-label="Temizlik"
              >
                <span>Temizlik</span>
              </button>
            </li>
            <li class={activeTab === 'clothing' ? 'is-active' : ''}>
              <button
                type="button"
                on:click={() => changeTab('clothing')}
                class="tab-button"
                aria-label="Giyim"
              >
                <span>Giyim</span>
              </button>
            </li>
          </ul>
        </div>

        <div class="tab-content">
          {#if activeTab === 'search'}
            <!-- Search Tab Content -->
            <div class="search-tab-content">
              <div class="field has-addons">
                <div class="control is-expanded has-icons-left autocomplete-container">
                  <input
                    bind:this={searchInput}
                    bind:value={searchTerm}
                    on:input={handleInputChange}
                    on:keydown={handleKeyDown}
                    on:blur={handleInputBlur}
                    class="input is-large"
                    type="text"
                    placeholder="Ürün adı, barkod veya ürün kodu ile arama yapın..."
                    disabled={isSearching}
                    autocomplete="off"
                  />
                  <span class="icon is-left">
                    <i class="fas fa-search"></i>
                  </span>
                </div>
                <div class="control">
                  <button
                    on:click={performSearch}
                    class="button is-primary is-large {isSearching ? 'is-loading' : ''}"
                  >
                    <span class="icon">
                      <i class="fas fa-plus"></i>
                    </span>
                    <span>Ekle</span>
                  </button>
                </div>
              </div>

              <!-- DSL Ürünleri Checkbox -->
              <div class="field" style="margin-top: 1rem;">
                <div class="control">
                  <label class="checkbox">
                    <input type="checkbox" bind:checked={showDLSProducts} />
                    <span style="margin-left: 0.5rem; font-weight: 500; color: #374151;">
                      DSL ürünleri listele
                    </span>
                  </label>
                </div>
              </div>
            </div>
          {:else}
            <!-- Category Tabs Content (placeholder grid) -->
            <div class="category-tab-content">
              <div class="category-grid">
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 1</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 2</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 3</span>
                </div>
                <div class="category-item">
                  <div class="category-image-placeholder">
                    <i class="fas fa-image"></i>
                  </div>
                  <span class="category-name">Ürün 4</span>
                </div>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <!-- Sales Items Table -->
      <div class="sales-table-section">
        {#if salesItems.length === 0}
          <div class="notification is-light">
            <div class="has-text-centered">
              <span class="icon is-large has-text-grey-light">
                <i class="fas fa-shopping-basket fa-3x"></i>
              </span>
              <p class="title is-6 has-text-grey">Henüz ürün eklenmedi</p>
              <p class="subtitle is-7 has-text-grey">
                Yukarıdaki arama kutusunu kullanarak ürün ekleyebilirsiniz
              </p>
            </div>
          </div>
        {:else}
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable sales-table">
              <thead>
                <tr>
                  <th class="col-sequence">Sıra</th>
                  <th class="col-product">Ürün</th>
                  <th class="col-unit">Birim</th>
                  <th class="col-price has-text-right">Fiyat</th>
                  <th class="col-quantity">Miktar</th>
                  <th class="col-total has-text-right">Toplam</th>
                  <th class="col-actions">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                {#each salesItems as item, index (item.sequenceNo)}
                  <tr>
                    <td>
                      <span class="tag is-rounded">{item.sequenceNo}</span>
                    </td>
                    <td>
                      <strong>{item.name}</strong>
                      <br />
                      <small class="has-text-grey">
                        {#if item.barcode}
                          | Barkod: {item.barcode}{/if}
                      </small>
                    </td>
                    <td>{item.unit}</td>
                    <td class="has-text-right">
                      <span class="decimal-aligned">
                        {item.price.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </span>
                    </td>
                    <td>
                      <div class="field has-addons quantity-controls">
                        <!-- Decrease Button -->
                        <div class="control">
                          <button
                            on:click={() => decreaseQuantity(index)}
                            class="button is-small quantity-btn decrement"
                            title="Miktarı azalt"
                          >
                            <span class="icon">
                              <i class="fas fa-minus"></i>
                            </span>
                          </button>
                        </div>

                        <!-- Quantity Input -->
                        <div class="control">
                          <input
                            value={formatQuantity(item.quantity)}
                            on:input={event => handleQuantityChange(index, event.target.value)}
                            on:keydown={event => handleQuantityKeydown(event, item.unit)}
                            class="input is-small has-text-centered"
                            type="text"
                            placeholder={isDecimalUnit(item.unit) ? '1,50' : '1'}
                            title={isDecimalUnit(item.unit)
                              ? `${item.unit} birimi için ondalık değer giriniz (örn: 1,50 veya 2,25)`
                              : `${item.unit} birimi için tam sayı giriniz (örn: 1, 2, 3)`}
                            style="width: 80px;"
                          />
                        </div>

                        <!-- Increase Button -->
                        <div class="control">
                          <button
                            on:click={() => increaseQuantity(index)}
                            class="button is-small quantity-btn increment"
                            title="Miktarı artır"
                          >
                            <span class="icon">
                              <i class="fas fa-plus"></i>
                            </span>
                          </button>
                        </div>
                      </div>
                    </td>
                    <td class="has-text-right">
                      <strong class="decimal-aligned">
                        {item.total.toLocaleString('tr-TR', {
                          style: 'currency',
                          currency: 'TRY',
                        })}
                      </strong>
                    </td>
                    <td>
                      <button
                        on:click={() => removeItem(index)}
                        class="button is-small is-danger is-outlined"
                        title="Ürünü kaldır"
                      >
                        <span class="icon">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {/if}
      </div>
    </div>

    <!-- Right Panel: POS Payment System (75%) -->
    <div class="content-panel">
      <!-- Payment Summary Header -->
      <div class="payment-header">
        <div class="payment-summary">
          <div class="summary-item">
            <span class="summary-label">İndirim Tutarı</span>
            <span class="summary-value discount">₺ 0</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Ödenen Tutar</span>
            <span class="summary-value paid"
              >{totalPaidAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}</span
            >
          </div>
        </div>
        <div class="total-remaining-container">
          <div class="total-amount-container tags has-addons">
            <span class="tag total-label">Toplam Tutar</span>
            <span class="tag total-amount decimal-aligned">
              {totalAmount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' })}
            </span>
          </div>
          <div class="remaining-amount-container tags has-addons">
            <span class="tag remaining-label">Kalanı Öde</span>
            <button
              class="tag remaining-amount-btn"
              class:active={remainingAmount > 0}
              disabled={remainingAmount <= 0}
              on:click={payRemainingAmount}
            >
              {remainingAmount.toLocaleString('tr-TR', {
                style: 'currency',
                currency: 'TRY',
              })}
            </button>
          </div>
        </div>
      </div>

      <!-- Payment Methods and Keypad -->
      <div class="payment-content">
        <!-- Left Side - Payment Methods -->
        <div class="payment-methods">
          <button class="payment-btn meal-card" on:click={() => selectPaymentMethod('meal-card')}
            >Yemek Kartı</button
          >
          <button
            class="payment-btn cash-drawer"
            on:click={() => selectPaymentMethod('cash-drawer')}>Para Çekmecesini Aç</button
          >

          <button class="payment-btn cash" on:click={() => selectPaymentMethod('cash')}
            >Nakit</button
          >
          <button
            class="payment-btn credit-card"
            on:click={() => selectPaymentMethod('credit-card')}>Kredi Kartı</button
          >
        </div>

        <!-- Center - Numeric Keypad -->
        <div class="numeric-keypad">
          <div class="amount-display">
            <input
              type="text"
              class="amount-input"
              bind:value={paymentAmount}
              placeholder="0"
              readonly
            />
          </div>
          <table class="keypad-table">
            <tbody>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('7')}>7</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('8')}>8</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('9')}>9</button></td>
                <td><button class="key-btn backspace" on:click={removeLastDigit}>⌫</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('4')}>4</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('5')}>5</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('6')}>6</button></td>
                <td><button class="key-btn clear" on:click={clearAmount}>C</button></td>
              </tr>
              <tr class="keypad-row">
                <td><button class="key-btn" on:click={() => addToAmount('1')}>1</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('2')}>2</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('3')}>3</button></td>
                <td><button class="key-btn" on:click={() => addToAmount('00')}>00</button></td>
              </tr>
              <tr class="keypad-row">
                <td colspan="2">
                  <button class="key-btn zero" on:click={() => addToAmount('0')}>0</button>
                </td>
                <td><button class="key-btn decimal" on:click={() => addToAmount('.')}>.</button></td
                >
                <td></td>
              </tr>
            </tbody>
          </table>

          <!-- Partial Payments List -->
          {#if partialPayments.length > 0}
            <div class="partial-payments">
              <h4>Yapılan Ödemeler:</h4>
              <div class="payments-list">
                {#each partialPayments as payment (payment.id)}
                  <div class="payment-item">
                    <span class="payment-method"
                      >{payment.method === 'cash'
                        ? 'Nakit'
                        : payment.method === 'credit-card'
                          ? 'Kredi Kartı'
                          : 'Yemek Kartı'}</span
                    >
                    <span class="payment-amount">{payment.amount.toFixed(2)} TL</span>
                    <button class="remove-payment" on:click={() => removePartialPayment(payment.id)}
                      >×</button
                    >
                  </div>
                {/each}
              </div>
              <div class="payments-total">
                <strong>Toplam Ödenen: {totalPaidAmount.toFixed(2)} TL</strong>
              </div>
              {#if partialPayments.length > 1}
                <button class="clear-all-payments" on:click={clearAllPayments}
                  >Tümünü Temizle</button
                >
              {/if}
            </div>
          {/if}

          <div class="sale-buttons">
            <button class="complete-sale-btn" on:click={completeSale}>Satışı Tamamla</button>
            <button class="demo-sale-btn" on:click={sendDemoSaleData}>Demo Satış Test</button>
            <button class="pair-device-btn" on:click={() => testDevicePairing()}>POS Bağla</button>
          </div>
        </div>

        <!-- Right Side - Quick Actions -->
        <div class="quick-actions">
          <button class="action-btn loyalty">Birlik Kart</button>
          <button class="action-btn premium">Premium Kart</button>
          <button class="action-btn gift">Hediye Çeki</button>
          <button class="action-btn receipt">Fiş Beklemeye Al</button>
          <button class="action-btn is-warning">Satış İptal</button>
          <button class="action-btn is-info">Cari Ekle</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Cash Change Modal -->
  {#if showCashChangeModal}
    <div class="modal is-active">
      <div
        class="modal-background"
        on:click={closeCashChangeModal}
        on:keydown={closeCashChangeModal}
        role="button"
        tabindex="0"
      ></div>
      <div class="modal-card">
        <header class="modal-card-head">
          <p class="modal-card-title">
            <span class="icon">
              <i class="fas fa-money-bill-wave"></i>
            </span>
            Nakit Ödeme
          </p>
          <button class="delete" aria-label="close" on:click={closeCashChangeModal}></button>
        </header>
        <section class="modal-card-body">
          <div class="cash-payment-info">
            <div class="field">
              <label class="label" for="sale-amount">Satış Tutarı</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-large">
                    {remainingAmount.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            <div class="field">
              <label class="label" for="cash-received">Alınan Nakit</label>
              <div class="control">
                <div class="tags has-addons">
                  <span class="tag is-primary is-large">
                    {cashReceived.toLocaleString('tr-TR', {
                      style: 'currency',
                      currency: 'TRY',
                    })}
                  </span>
                </div>
              </div>
            </div>

            {#if changeAmount > 0}
              <div class="field">
                <label class="label" for="change-amount">Para Üstü</label>
                <div class="control">
                  <div class="tags has-addons">
                    <span class="tag is-success is-large">
                      {changeAmount.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY',
                      })}
                    </span>
                  </div>
                </div>
              </div>
            {/if}

            {#if cashReceived < remainingAmount}
              <div class="notification is-danger is-light">
                <span class="icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </span>
                <span>Alınan nakit tutar yetersiz!</span>
              </div>
            {/if}
          </div>
        </section>
        <footer class="modal-card-foot">
          <button
            class="button is-success is-large"
            on:click={completeCashSale}
            disabled={cashReceived < remainingAmount}
          >
            <span class="icon">
              <i class="fas fa-check"></i>
            </span>
            <span>Satışı Tamamla</span>
          </button>
          <button class="button" on:click={closeCashChangeModal}>İptal</button>
        </footer>
      </div>
    </div>
  {/if}
</div>

<style>
  @import './Home.css';
</style>
