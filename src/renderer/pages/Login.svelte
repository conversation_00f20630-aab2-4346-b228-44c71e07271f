<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { showError, showInfo } from '../utils/toastUtils.js'

  // Form state
  let username = ''
  let password = ''
  let isLoading = false
  let showPassword = false

  // Validation state
  let usernameError = ''
  let passwordError = ''
  let formError = ''

  // Modal state
  let showSettingsModal = false

  // File input state for data import
  const fileInputs = {
    employees: null,
    roles: null,
    permissions: null,
    employee_roles: null,
    role_permissions: null,
  }

  // Import status
  const importStatus = {
    isImporting: false,
    lastImport: null,
    errorMessage: '',
  }

  // Component lifecycle
  onMount(() => {
    // Check if user is already authenticated
    checkAuthStatus()
  })

  async function checkAuthStatus() {
    try {
      const isAuthenticated = await authStore.checkAuth()
      if (isAuthenticated) {
        showInfo('<PERSON>aten giri<PERSON> ya<PERSON>, yönlendiriliyor...')
        setTimeout(() => push('/'), 1000)
      }
    } catch (error) {
      console.error('Error checking auth status:', error)
    }
  }

  function validateForm() {
    let isValid = true

    // Reset errors
    usernameError = ''
    passwordError = ''
    formError = ''

    // Validate username
    if (!username.trim()) {
      usernameError = 'Kullanıcı kodu gereklidir'
      isValid = false
    } else if (username.trim().length < 3) {
      usernameError = 'Kullanıcı kodu en az 3 karakter olmalıdır'
      isValid = false
    }

    // Validate password
    if (!password) {
      passwordError = 'Şifre gereklidir'
      isValid = false
    } else if (password.length < 3) {
      passwordError = 'Şifre en az 3 karakter olmalıdır'
      isValid = false
    }

    return isValid
  }

  async function handleLogin() {
    if (!validateForm()) {
      showError('Lütfen form hatalarını düzeltin ve tekrar deneyin')
      return
    }

    isLoading = true
    formError = ''

    try {
      const credentials = {
        username: username.trim(),
        password,
      }

      const result = await authStore.login(credentials)

      if (result.success) {
        // Clear password for security
        password = ''

        showInfo(result.message)
        // Redirect to home page (authStore already handles user data)
        setTimeout(() => {
          push('/home')
        }, 100) // Small delay to ensure store is updated
      } else {
        formError =
          result.message || 'Giriş başarısız. Lütfen kullanıcı bilgilerinizi kontrol edin.'
      }
    } catch (error) {
      console.error('Login error:', error)
      formError = 'Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.'
      showError(formError)
    } finally {
      isLoading = false
    }
  }

  function togglePasswordVisibility() {
    showPassword = !showPassword
  }

  function handleKeyPress(event) {
    if (event.key === 'Enter') {
      handleLogin()
    }
  }

  function clearForm() {
    username = ''
    password = ''
    usernameError = ''
    passwordError = ''
    formError = ''
  }

  // Modal functions
  function openSettingsModal() {
    showSettingsModal = true
    console.log('🔧 Ayarlar modalı açıldı')
  }

  function closeSettingsModal() {
    showSettingsModal = false
    console.log('🔧 Ayarlar modalı kapatıldı')
  }

  async function handleDbSync() {
    console.log('📊 DB Aktar işlemi başlatıldı')
    try {
      // DB sync işlemi burada yapılacak
      showInfo('Veritabanı senkronizasyonu başlatıldı...')

      // IPC call to main process for DB sync
      const result = await window.electronAPI.syncInventory('localhost')

      if (result.success) {
        showInfo(result.message || 'Veritabanı başarıyla senkronize edildi!')
      } else {
        showError(result.error || 'Veritabanı senkronizasyonu başarısız oldu.')
      }
    } catch (error) {
      console.error('DB sync error:', error)
      showError('Veritabanı senkronizasyonu sırasında hata oluştu.')
    }
  }

  // Exit application function
  async function handleExit() {
    console.log('🚪 Uygulama kapatılıyor...')
    try {
      showInfo('Uygulama kapatılıyor...')

      // IPC call to main process to close the application
      if (window.electronAPI && window.electronAPI.closeApp) {
        await window.electronAPI.closeApp()
      } else {
        // Fallback for development or if IPC is not available
        window.close()
      }
    } catch (error) {
      console.error('Exit error:', error)
      // Force close as fallback
      window.close()
    }
  }

  // File handling functions
  function handleFileSelect(event, fileType) {
    const file = event.target.files[0]
    if (file && file.type === 'application/json') {
      fileInputs[fileType] = file
      console.log(`📁 Selected ${fileType} file:`, file.name)
    } else {
      showError('Lütfen geçerli bir JSON dosyası seçin')
      event.target.value = ''
    }
  }

  async function handleFileImport() {
    if (importStatus.isImporting) return

    // Check if any files are selected
    const selectedFiles = Object.values(fileInputs).filter(file => file !== null)
    if (selectedFiles.length === 0) {
      showError('Lütfen en az bir JSON dosyası seçin')
      return
    }

    importStatus.isImporting = true
    importStatus.errorMessage = ''

    try {
      showInfo('Dosyalar okunuyor...')

      // Read all selected files
      const importData = {}

      for (const [fileType, file] of Object.entries(fileInputs)) {
        if (file) {
          console.log(`📖 Reading ${fileType} file...`)
          const fileContent = await readFileAsText(file)
          const jsonData = JSON.parse(fileContent)

          // Extract the array from the JSON structure
          if (jsonData[fileType] && Array.isArray(jsonData[fileType])) {
            importData[fileType] = jsonData[fileType]
            console.log(`✅ Loaded ${jsonData[fileType].length} ${fileType} records`)
          } else {
            console.warn(`⚠️ No ${fileType} array found in file ${file.name}`)
          }
        }
      }

      console.log('📥 Starting import to database...', importData)

      // Import to database via IPC
      const result = await window.electronAPI.importUserData(importData)

      if (result.success) {
        const stats = result.result
        const message = `Veri aktarımı başarılı!
        Kullanıcılar: ${stats.employees || 0}
        Roller: ${stats.roles || 0}
        İzinler: ${stats.permissions || 0}
        Kullanıcı Rolleri: ${stats.employee_roles || 0}
        Rol İzinleri: ${stats.role_permissions || 0}`

        showInfo(message)
        importStatus.lastImport = new Date().toLocaleString('tr-TR')

        // Clear file selections
        Object.keys(fileInputs).forEach(key => {
          fileInputs[key] = null
        })

        // Clear file inputs
        const fileInputElements = document.querySelectorAll('input[type="file"]')
        fileInputElements.forEach(input => (input.value = ''))
      } else {
        importStatus.errorMessage = result.error || 'Bilinmeyen hata oluştu'
        showError(`Veri aktarımı başarısız: ${importStatus.errorMessage}`)
      }
    } catch (error) {
      console.error('📥 Import error:', error)
      importStatus.errorMessage = error.message
      showError(`Dosya okuma hatası: ${error.message}`)
    } finally {
      importStatus.isImporting = false
    }
  }

  function readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = e => resolve(e.target.result)
      reader.onerror = e => reject(new Error('Dosya okunamadı'))
      reader.readAsText(file)
    })
  }
</script>

<div class="container">
  <div class="columns is-centered">
    <div class="column is-three-fifths-tablet is-two-fifths-desktop">
      <div class="box login-box">
        <!-- Logo Images - Side by Side -->
        <div class="logo-container">
          <div class="logo-left">
            <img src="/furpaLogo.png" alt="Furpa Logo" class="login-logo" />
          </div>
          <div class="logo-right">
            <img src="/depoYazilimLogo.png" alt="Depoyazılım Logo" class="login-logo" />
          </div>
        </div>
        <h3 class="title is-4 has-text-centered has-text-white">
          <span class="icon">
            <i class="fas fa-user-circle"></i>
          </span>
          Personel Girişi
        </h3>

        {#if formError}
          <div class="notification is-danger is-light">
            <span class="icon">
              <i class="fas fa-exclamation-triangle"></i>
            </span>
            <span>{formError}</span>
          </div>
        {/if}

        <form on:submit|preventDefault={handleLogin}>
          <!-- Username Field -->
          <div class="field">
            <label class="label" for="username">Kullanıcı Kodu</label>
            <div class="control has-icons-left">
              <input
                id="username"
                class="input {usernameError ? 'is-danger' : username.trim() ? 'is-success' : ''}"
                type="text"
                bind:value={username}
                on:keypress={handleKeyPress}
                placeholder="Kullanıcı kodunu giriniz..."
                disabled={isLoading}
              />
              <span class="icon is-small is-left">
                <i class="fas fa-user"></i>
              </span>
            </div>
            {#if usernameError}
              <p class="help is-danger">
                <span class="icon">
                  <i class="fas fa-exclamation-circle"></i>
                </span>
                <span>{usernameError}</span>
              </p>
            {/if}
          </div>

          <!-- Password Field -->
          <div class="field">
            <label class="label" for="password">Şifre</label>
            <div class="control has-icons-left has-icons-right">
              {#if showPassword}
                <input
                  id="password"
                  class="input {passwordError ? 'is-danger' : password ? 'is-success' : ''}"
                  type="text"
                  bind:value={password}
                  on:keypress={handleKeyPress}
                  placeholder="Şifrenizi giriniz..."
                  disabled={isLoading}
                />
              {:else}
                <input
                  id="password"
                  class="input {passwordError ? 'is-danger' : password ? 'is-success' : ''}"
                  type="password"
                  bind:value={password}
                  on:keypress={handleKeyPress}
                  placeholder="Şifrenizi giriniz..."
                  disabled={isLoading}
                />
              {/if}
              <span class="icon is-small is-left">
                <i class="fas fa-lock"></i>
              </span>
              <button
                type="button"
                class="button is-white is-small password-toggle"
                on:click={togglePasswordVisibility}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
                disabled={isLoading}
              >
                <span class="icon is-small">
                  <i class="fas {showPassword ? 'fa-eye-slash' : 'fa-eye'}"></i>
                </span>
              </button>
            </div>
            {#if passwordError}
              <p class="help is-danger">
                <span class="icon">
                  <i class="fas fa-exclamation-circle"></i>
                </span>
                <span>{passwordError}</span>
              </p>
            {/if}
          </div>

          <!-- Login Button -->
          <div class="field">
            <div class="control">
              <button
                type="submit"
                class="button is-primary is-fullwidth {isLoading ? 'is-loading' : ''}"
                disabled={isLoading}
              >
                <span class="icon">
                  <i class="fas fa-sign-in-alt"></i>
                </span>
                <span>Giriş</span>
              </button>
            </div>
          </div>

          <!-- All Actions in Single Row -->
          <div class="field button-container">
            <div class="control button-item">
              <button
                id="button1"
                type="button"
                class="button is-light is-small"
                on:click={clearForm}
                disabled={isLoading}
              >
                <span class="icon">
                  <i class="fas fa-eraser"></i>
                </span>
                <span>Temizle</span>
              </button>
            </div>
            <div class="control button-item">
              <button
                id="button3"
                type="button"
                class="button is-danger is-small"
                on:click={handleExit}
                disabled={isLoading}
                title="Uygulamayı Kapat"
              >
                <span class="icon">
                  <i class="fas fa-power-off"></i>
                </span>
                <span>Çıkış</span>
              </button>
            </div>
            <div class="control button-item">
              <button
                id="button2"
                type="button"
                class="button is-light is-small"
                on:click={openSettingsModal}
                disabled={isLoading}
              >
                <span class="icon">
                  <i class="fas fa-cog"></i>
                </span>
                <span>Ayarlar</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Settings Modal -->
{#if showSettingsModal}
  <div class="modal is-active">
    <div
      class="modal-background"
      on:click={closeSettingsModal}
      on:keydown={e => e.key === 'Escape' && closeSettingsModal()}
      role="button"
      tabindex="0"
      aria-label="Close modal"
    ></div>
    <div class="modal-card">
      <header class="modal-card-head">
        <p class="modal-card-title">
          <span class="icon">
            <i class="fas fa-cog"></i>
          </span>
          Ayarlar
        </p>
        <button class="delete" aria-label="close" on:click={closeSettingsModal}></button>
      </header>
      <section class="modal-card-body">
        <div class="content">
          <!-- Database Sync Section -->
          <div class="mb-6">
            <h4 class="title is-5">
              <span class="icon">
                <i class="fas fa-database"></i>
              </span>
              Veritabanı İşlemleri
            </h4>
            <p class="subtitle is-6">
              PostgreSQL sunucusundan verileri yerel SQLite veritabanına aktarın.
            </p>

            <div class="notification is-info is-light">
              <span class="icon">
                <i class="fas fa-info-circle"></i>
              </span>
              <span>
                Bu işlem inventory, barkod tanımları, fiyat listesi ve kategori verilerini
                senkronize eder.
              </span>
            </div>

            <button class="button is-success" on:click={handleDbSync}>
              <span class="icon">
                <i class="fas fa-download"></i>
              </span>
              <span>DB Aktar</span>
            </button>
          </div>

          <!-- User Data Import Section -->
          <div class="mb-6">
            <h4 class="title is-5">
              <span class="icon">
                <i class="fas fa-users"></i>
              </span>
              Kullanıcı ve Yetki Verileri İçe Aktarma
            </h4>
            <p class="subtitle is-6">
              JSON dosyalarından kullanıcı, rol ve yetki verilerini içe aktarın.
            </p>

            {#if importStatus.lastImport}
              <div class="notification is-success is-light mb-3">
                <span class="icon">
                  <i class="fas fa-check-circle"></i>
                </span>
                <span>Son aktarım: {importStatus.lastImport}</span>
              </div>
            {/if}

            {#if importStatus.errorMessage}
              <div class="notification is-danger is-light mb-3">
                <span class="icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </span>
                <span>{importStatus.errorMessage}</span>
              </div>
            {/if}

            <!-- File Upload Section -->
            <div class="columns is-multiline">
              <div class="column is-half">
                <div class="field">
                  <label class="label" for="employees-file">Kullanıcılar (employees.json)</label>
                  <div class="control">
                    <input
                      id="employees-file"
                      class="input"
                      type="file"
                      accept=".json"
                      on:change={e => handleFileSelect(e, 'employees')}
                      disabled={importStatus.isImporting}
                    />
                  </div>
                </div>
              </div>

              <div class="column is-half">
                <div class="field">
                  <label class="label" for="roles-file">Roller (roles.json)</label>
                  <div class="control">
                    <input
                      id="roles-file"
                      class="input"
                      type="file"
                      accept=".json"
                      on:change={e => handleFileSelect(e, 'roles')}
                      disabled={importStatus.isImporting}
                    />
                  </div>
                </div>
              </div>

              <div class="column is-half">
                <div class="field">
                  <label class="label" for="permissions-file">İzinler (permissions.json)</label>
                  <div class="control">
                    <input
                      id="permissions-file"
                      class="input"
                      type="file"
                      accept=".json"
                      on:change={e => handleFileSelect(e, 'permissions')}
                      disabled={importStatus.isImporting}
                    />
                  </div>
                </div>
              </div>

              <div class="column is-half">
                <div class="field">
                  <label class="label" for="employee-roles-file"
                    >Kullanıcı Rolleri (employee_roles.json)</label
                  >
                  <div class="control">
                    <input
                      id="employee-roles-file"
                      class="input"
                      type="file"
                      accept=".json"
                      on:change={e => handleFileSelect(e, 'employee_roles')}
                      disabled={importStatus.isImporting}
                    />
                  </div>
                </div>
              </div>

              <div class="column is-full">
                <div class="field">
                  <label class="label" for="role-permissions-file"
                    >Rol İzinleri (role_permissions.json)</label
                  >
                  <div class="control">
                    <input
                      id="role-permissions-file"
                      class="input"
                      type="file"
                      accept=".json"
                      on:change={e => handleFileSelect(e, 'role_permissions')}
                      disabled={importStatus.isImporting}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="field">
              <div class="control">
                <button
                  class="button is-primary {importStatus.isImporting ? 'is-loading' : ''}"
                  on:click={handleFileImport}
                  disabled={importStatus.isImporting}
                >
                  <span class="icon">
                    <i class="fas fa-upload"></i>
                  </span>
                  <span>Verileri İçe Aktar</span>
                </button>
              </div>
            </div>

            <div class="notification is-warning is-light">
              <span class="icon">
                <i class="fas fa-exclamation-triangle"></i>
              </span>
              <span> Bu işlem mevcut verilerin üzerine yazacaktır. Lütfen dikkatli olun. </span>
            </div>
          </div>
        </div>
      </section>
      <footer class="modal-card-foot">
        <button class="button" on:click={closeSettingsModal}>
          <span class="icon">
            <i class="fas fa-times"></i>
          </span>
          <span>Kapat</span>
        </button>
      </footer>
    </div>
  </div>
{/if}

<style>
  .label {
    color: white !important;
  }
  .password-toggle {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    z-index: 4;
  }

  .password-toggle:hover {
    background: rgba(0, 0, 0, 0.05) !important;
  }

  /* Enhanced form styling */
  .box {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background-color: rgba(66, 66, 66, 0.8) !important; /* Gray background with 50% opacity */
  }

  /* Login box positioning - increased top margin for better visual balance */
  .login-box {
    margin-top: 120px !important; /* Significantly increased from 50px */
    margin-bottom: 40px !important; /* Add bottom margin for balance */
    margin-left: auto !important;
    margin-right: auto !important;
  }

  /* Logo container - side by side layout */
  .logo-container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 30px !important;
    padding: 0 20px !important;
    gap: 20px !important;
  }

  .logo-left {
    flex: 1 !important;
    display: flex !important;
    justify-content: flex-start !important;
    align-items: center !important;
  }

  .logo-right {
    flex: 1 !important;
    display: flex !important;
    justify-content: flex-end !important;
    align-items: center !important;
  }

  .login-logo {
    max-height: 60px !important;
    max-width: 150px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
  }

  /* Button container - three buttons in a row layout */
  .button-container {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-top: 20px !important;
    padding: 0 !important;
    gap: 15px !important;
  }

  .button-item {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    flex: 1 !important;
  }

  /* Button styling for better appearance */
  #button1,
  #button2,
  #button3 {
    min-width: 100px !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
  }

  #button1:hover,
  #button2:hover,
  #button3:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* Button specific styling - no separate exit button container needed */

  #button3 {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    border: none !important;
    color: white !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  }

  #button3:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4) !important;
  }

  .input {
    padding-left: 3.25rem !important; /* 20px + icon space */
  }

  .input:focus {
    box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
  }

  .button.is-primary {
    background: linear-gradient(135deg, #800020 0%, #1e3a8a 100%);
    border: none;
    font-weight: 600;
    padding: 1.2em 2em;
    font-size: 1.1rem;
    border-radius: 0.5rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .button.is-primary:hover {
    background: linear-gradient(135deg, #8b1538 0%, #3b82f6 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(128, 0, 32, 0.4);
  }

  .notification {
    border-radius: 8px;
  }

  /* Import section styling */
  .mb-6 {
    margin-bottom: 1.5rem !important;
  }

  .mb-3 {
    margin-bottom: 0.75rem !important;
  }

  .label {
    color: #363636 !important;
    font-weight: 600;
    font-size: 0.9rem;
  }

  input[type='file'] {
    border: 2px dashed #dbdbdb;
    border-radius: 6px;
    padding: 0.75rem;
    transition: all 0.3s ease;
  }

  input[type='file']:hover {
    border-color: #3273dc;
    background-color: #f7f9fc;
  }

  input[type='file']:focus {
    border-color: #3273dc;
    box-shadow: 0 0 0 2px rgba(50, 115, 220, 0.2);
  }

  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .column.is-three-fifths-tablet {
      padding: 1rem;
    }

    .login-box {
      margin-top: 80px !important; /* Reduced but still substantial margin for mobile */
      margin-bottom: 30px !important;
      margin-left: 0.5rem !important;
      margin-right: 0.5rem !important;
    }

    /* Mobile logo adjustments */
    .logo-container {
      padding: 0 10px !important;
      gap: 15px !important;
      margin-bottom: 20px !important;
    }

    .login-logo {
      max-height: 45px !important;
      max-width: 120px !important;
    }

    /* Mobile button adjustments */
    .button-container {
      gap: 10px !important;
      margin-top: 15px !important;
    }

    #button1,
    #button2,
    #button3 {
      min-width: 80px !important;
      font-size: 0.8rem !important;
    }
  }

  /* Additional responsive breakpoint for very small screens */
  @media screen and (max-width: 480px) {
    .login-box {
      margin-top: 60px !important; /* Further reduced for very small screens */
      margin-bottom: 20px !important;
      margin-left: 0.25rem !important;
      margin-right: 0.25rem !important;
    }

    /* Very small screen logo adjustments */
    .logo-container {
      padding: 0 5px !important;
      gap: 10px !important;
      margin-bottom: 15px !important;
    }

    .login-logo {
      max-height: 35px !important;
      max-width: 100px !important;
    }

    /* Very small screen button adjustments */
    .button-container {
      gap: 5px !important;
      margin-top: 10px !important;
    }

    #button1,
    #button2,
    #button3 {
      min-width: 70px !important;
      font-size: 0.75rem !important;
      padding: 0.5rem 0.75rem !important;
    }
  }

  /* Large screens - even more top margin for better visual balance */
  @media screen and (min-width: 1200px) {
    .login-box {
      margin-top: 150px !important; /* Larger margin for big screens */
      margin-bottom: 50px !important;
    }
  }

  /* Modal styling */
  .modal-card {
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .modal-card-head {
    background: linear-gradient(135deg, #800020 0%, #1e3a8a 100%);
    color: white;
    border-radius: 12px 12px 0 0;
  }

  .modal-card-title {
    color: white !important;
    font-weight: 600;
  }

  .modal-card-body {
    padding: 2rem;
  }

  .modal-card-foot {
    border-radius: 0 0 12px 12px;
    padding: 1.5rem;
    gap: 1rem;
  }

  .button.is-success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .button.is-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
  }
</style>
