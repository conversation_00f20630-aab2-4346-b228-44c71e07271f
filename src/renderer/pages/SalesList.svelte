<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { getSaleDetails, getSalesWithFilters } from '../utils/database.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  // Sales state
  let salesData = { sales: [], pagination: { page: 1, totalPages: 1, total: 0 } }
  let isLoading = false

  // Filter state
  let searchTerm = ''
  let dateFrom = ''
  let dateTo = ''
  let sortBy = 'created_at'
  let sortOrder = 'DESC'
  let currentPage = 1
  const pageSize = 20

  // Date filter presets
  let selectedDateFilter = 'today'

  // Sale details modal state
  let showSaleDetailsModal = false
  let selectedSaleDetails = null
  let isLoadingSaleDetails = false

  onMount(async () => {
    setDateFilter('today')
    await loadSales()
  })

  function setDateFilter(filter) {
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]
    let weekStart, monthStart

    switch (filter) {
      case 'today':
        dateFrom = todayStr
        dateTo = todayStr
        break
      case 'week':
        weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay())
        dateFrom = weekStart.toISOString().split('T')[0]
        dateTo = todayStr
        break
      case 'month':
        monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        dateFrom = monthStart.toISOString().split('T')[0]
        dateTo = todayStr
        break
      case 'all':
        dateFrom = ''
        dateTo = ''
        break
    }
    selectedDateFilter = filter
  }

  async function loadSales() {
    isLoading = true
    try {
      const options = {
        dateFrom: dateFrom || undefined,
        dateTo: dateTo || undefined,
        searchTerm: searchTerm.trim(),
        sortBy,
        sortOrder,
        page: currentPage,
        limit: pageSize,
      }

      salesData = await getSalesWithFilters(options)
      console.log('📊 Satışlar yüklendi:', salesData)

      if (salesData.sales.length > 0) {
        showSuccess(`${salesData.sales.length} satış kaydı yüklendi`)
      }
    } catch (error) {
      console.error('❌ Satışlar yüklenirken hata:', error)
      showError('Satışlar yüklenirken hata oluştu')
    } finally {
      isLoading = false
    }
  }

  async function handleSearch() {
    currentPage = 1
    await loadSales()
  }

  async function handleSort(column) {
    if (sortBy === column) {
      sortOrder = sortOrder === 'ASC' ? 'DESC' : 'ASC'
    } else {
      sortBy = column
      sortOrder = 'DESC'
    }
    currentPage = 1
    await loadSales()
  }

  async function changePage(page) {
    currentPage = page
    await loadSales()
  }

  async function handleDateFilterChange() {
    currentPage = 1
    await loadSales()
  }

  function formatTime(dateString) {
    const date = new Date(dateString)
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  function formatDate(dateString) {
    const date = new Date(dateString)
    return date.toLocaleDateString('tr-TR')
  }

  function formatCurrency(amount) {
    return amount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    })
  }

  function getSortIcon(column) {
    if (sortBy !== column) return 'fas fa-sort'
    return sortOrder === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down'
  }

  // Sale details modal functions
  async function openSaleDetails(saleUuid) {
    showSaleDetailsModal = true
    isLoadingSaleDetails = true
    selectedSaleDetails = null

    try {
      selectedSaleDetails = await getSaleDetails(saleUuid)
      console.log('📋 Sale details loaded:', selectedSaleDetails)
    } catch (error) {
      console.error('❌ Error loading sale details:', error)
      showError('Satış detayları yüklenirken hata oluştu')
      showSaleDetailsModal = false
    } finally {
      isLoadingSaleDetails = false
    }
  }

  function closeSaleDetails() {
    showSaleDetailsModal = false
    selectedSaleDetails = null
    isLoadingSaleDetails = false
  }

  // Generate page numbers for pagination
  $: pageNumbers = (() => {
    const pages = []
    const total = salesData.pagination.totalPages
    const current = currentPage

    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i)
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i)
        pages.push('...')
        pages.push(total)
      } else if (current >= total - 3) {
        pages.push(1)
        pages.push('...')
        for (let i = total - 4; i <= total; i++) pages.push(i)
      } else {
        pages.push(1)
        pages.push('...')
        for (let i = current - 1; i <= current + 1; i++) pages.push(i)
        pages.push('...')
        pages.push(total)
      }
    }
    return pages
  })()
</script>

<div class="sales-list-page">
  <!-- Simple Header -->

  <div class="section">
    <div class="container">
      <h1 class="page-title mb-4">
        <i class="fas fa-list-alt"></i>
        Satış Listesi
      </h1>
      <!-- Compact Filters -->
      <div class="filters-card">
        <div class="filter-row">
          <!-- Date Preset -->
          <div class="filter-group">
            <select
              class="filter-select"
              bind:value={selectedDateFilter}
              on:change={() => {
                setDateFilter(selectedDateFilter)
                handleDateFilterChange()
              }}
            >
              <option value="today">Bugün</option>
              <option value="week">Bu Hafta</option>
              <option value="month">Bu Ay</option>
              <option value="all">Tümü</option>
              <option value="custom">Özel Tarih</option>
            </select>
          </div>

          <!-- Custom Date Range -->
          {#if selectedDateFilter === 'custom'}
            <div class="filter-group">
              <input
                class="filter-input"
                type="date"
                placeholder="Başlangıç"
                bind:value={dateFrom}
                on:change={handleDateFilterChange}
              />
            </div>
            <div class="filter-group">
              <input
                class="filter-input"
                type="date"
                placeholder="Bitiş"
                bind:value={dateTo}
                on:change={handleDateFilterChange}
              />
            </div>
          {/if}

          <!-- Search -->
          <div class="filter-group search-group">
            <div class="search-box">
              <input
                class="search-input"
                type="text"
                placeholder="Fiş no, personel ara..."
                bind:value={searchTerm}
                on:keydown={e => e.key === 'Enter' && handleSearch()}
              />
              <button class="search-btn" on:click={handleSearch} disabled={isLoading}>
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>

          <!-- Refresh -->
          <div class="filter-group">
            <button class="refresh-btn" on:click={loadSales} disabled={isLoading}>
              <i class="fas fa-sync-alt {isLoading ? 'fa-spin' : ''}"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Sales Content -->
      {#if isLoading}
        <div class="loading-card">
          <div class="loading-content">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
            <p>Yükleniyor...</p>
          </div>
        </div>
      {:else if salesData.sales.length === 0}
        <div class="empty-card">
          <i class="fas fa-inbox fa-2x"></i>
          <p>Satış bulunamadı</p>
        </div>
      {:else}
        <!-- Sales Table -->
        <div class="sales-card">
          <!-- Stats Header -->
          <div class="stats-header">
            <div class="stat">
              <span class="stat-label">Toplam</span>
              <span class="stat-value">{salesData.pagination.total}</span>
            </div>
            <div class="stat">
              <span class="stat-label">Tutar</span>
              <span class="stat-value">
                {formatCurrency(salesData.sales.reduce((sum, sale) => sum + sale.total_price, 0))}
              </span>
            </div>
            <div class="stat">
              <span class="stat-label">Sayfa</span>
              <span class="stat-value">{currentPage}/{salesData.pagination.totalPages}</span>
            </div>
          </div>

          <!-- Simple Table -->
          <div class="table-wrapper">
            <table class="sales-table">
              <thead>
                <tr>
                  <th>
                    <button class="sort-btn" on:click={() => handleSort('receipt_number')}>
                      Fiş No <i class={getSortIcon('receipt_number')}></i>
                    </button>
                  </th>
                  <th>
                    <button class="sort-btn" on:click={() => handleSort('created_at')}>
                      Tarih <i class={getSortIcon('created_at')}></i>
                    </button>
                  </th>
                  <th>
                    <button class="sort-btn" on:click={() => handleSort('total_price')}>
                      Tutar <i class={getSortIcon('total_price')}></i>
                    </button>
                  </th>
                  <th>Ödeme</th>
                  <th>
                    <button class="sort-btn" on:click={() => handleSort('employee_name')}>
                      Personel <i class={getSortIcon('employee_name')}></i>
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody>
                {#each salesData.sales as sale (sale.id)}
                  <tr>
                    <td>
                      <button
                        class="receipt-btn"
                        on:click={() => openSaleDetails(sale.uuid)}
                        title="Detayları görüntüle"
                      >
                        {sale.receipt_number}
                      </button>
                    </td>
                    <td>
                      <div class="date-info">
                        <div class="date">{formatDate(sale.created_at)}</div>
                        <div class="time">{formatTime(sale.created_at)}</div>
                      </div>
                    </td>
                    <td class="amount">{formatCurrency(sale.total_price)}</td>
                    <td>
                      <span class="payment-tag">{sale.payment_methods || 'Bilinmiyor'}</span>
                    </td>
                    <td>
                      <span class="employee-tag">
                        {sale.employee_full_name || sale.employee_code || 'Bilinmiyor'}
                      </span>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>

          <!-- Simple Pagination -->
          {#if salesData.pagination.totalPages > 1}
            <div class="pagination-wrapper">
              <button
                class="page-btn"
                disabled={!salesData.pagination.hasPrev || isLoading}
                on:click={() => changePage(currentPage - 1)}
              >
                <i class="fas fa-chevron-left"></i>
              </button>

              <div class="page-numbers">
                {#each pageNumbers as pageNum (pageNum)}
                  {#if pageNum === '...'}
                    <span class="page-ellipsis">...</span>
                  {:else}
                    <button
                      class="page-btn {pageNum === currentPage ? 'active' : ''}"
                      on:click={() => changePage(pageNum)}
                      disabled={isLoading}
                    >
                      {pageNum}
                    </button>
                  {/if}
                {/each}
              </div>

              <button
                class="page-btn"
                disabled={!salesData.pagination.hasNext || isLoading}
                on:click={() => changePage(currentPage + 1)}
              >
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Sale Details Modal -->
{#if showSaleDetailsModal}
  <button class="modal-overlay" on:click={closeSaleDetails} on:keydown={closeSaleDetails}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h3 class="modal-title">
          <i class="fas fa-receipt"></i>
          Satış Detayları
          {#if selectedSaleDetails}
            - {selectedSaleDetails.receipt_number}
          {/if}
        </h3>
        <button class="modal-close" on:click={closeSaleDetails}>
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-body">
        {#if isLoadingSaleDetails}
          <div class="loading-content">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
            <p>Satış detayları yükleniyor...</p>
          </div>
        {:else if selectedSaleDetails}
          <!-- Sale Info -->
          <div class="detail-section">
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">Fiş Numarası</div>
                <div class="detail-value primary">{selectedSaleDetails.receipt_number}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Tarih/Saat</div>
                <div class="detail-value">
                  {formatDate(selectedSaleDetails.created_at)} - {formatTime(
                    selectedSaleDetails.created_at
                  )}
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Personel</div>
                <div class="detail-value">
                  {selectedSaleDetails.employee_full_name ||
                    selectedSaleDetails.employee_code ||
                    'Bilinmiyor'}
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">Toplam Tutar</div>
                <div class="detail-value success">
                  {formatCurrency(selectedSaleDetails.total_price)}
                </div>
              </div>
            </div>
          </div>

          <!-- Products -->
          <div class="detail-section">
            <h4 class="section-title">
              <i class="fas fa-shopping-basket"></i>
              Ürünler ({selectedSaleDetails.total_items})
            </h4>
            <div class="products-table">
              <table>
                <thead>
                  <tr>
                    <th>Ürün</th>
                    <th>Miktar</th>
                    <th>Birim Fiyat</th>
                    <th>Toplam</th>
                  </tr>
                </thead>
                <tbody>
                  {#each selectedSaleDetails.items as item (item.id)}
                    <tr>
                      <td>
                        <div class="product-info">
                          <div class="product-name">{item.item_display_name}</div>
                          {#if item.barcode}
                            <div class="product-detail">Barkod: {item.barcode}</div>
                          {/if}
                          {#if item.inventory_code}
                            <div class="product-detail">Kod: {item.inventory_code}</div>
                          {/if}
                        </div>
                      </td>
                      <td>
                        <span class="quantity-tag {item.has_weight ? 'weight' : ''}">
                          {item.has_weight ? item.formatted_quantity : item.quantity}
                          {item.unit_display}
                        </span>
                      </td>
                      <td class="amount">{formatCurrency(item.unit_price)}</td>
                      <td class="amount total">{formatCurrency(item.total_price)}</td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          </div>

          <!-- Payment -->
          <div class="detail-section">
            <h4 class="section-title">
              <i class="fas fa-credit-card"></i>
              Ödeme Bilgileri
            </h4>
            <div class="payment-info">
              <div class="payment-methods">
                {#each Object.entries(selectedSaleDetails.payment_summary) as [method, amount] (method)}
                  <div class="payment-method">
                    <span class="method-name">{method}</span>
                    <span class="method-amount">{formatCurrency(amount)}</span>
                  </div>
                {/each}
              </div>
              <div class="payment-total">
                <span class="total-label">Toplam</span>
                <span class="total-amount">{formatCurrency(selectedSaleDetails.total_price)}</span>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <div class="modal-footer">
        <button class="btn-secondary" on:click={closeSaleDetails}>Kapat</button>
      </div>
    </div>
  </button>
{/if}

<style>
  /* Reset and Variables */
  :root {
    --primary-color: #e61e54;
    --primary-hover: #c91653;
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #e9ecef;
    --text-color: #333333;
    --text-muted: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --border-radius: 8px;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    --transition: all 0.2s ease;
  }

  .sales-list-page {
    min-height: 100vh;
    background: var(--bg-color);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Header */
  .page-header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .page-title i {
    color: var(--primary-color);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Filters */
  .filters-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
  }

  .filter-row {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
  }

  .search-group {
    flex: 1;
    min-width: 250px;
  }

  .filter-select,
  .filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    transition: var(--transition);
    background: var(--card-bg);
  }

  .filter-select:focus,
  .filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(230, 30, 84, 0.1);
  }

  .search-box {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
  }

  .search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(230, 30, 84, 0.1);
  }

  .search-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: none;
    outline: none;
    font-size: 0.9rem;
  }

  .search-btn,
  .refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refresh-btn {
    border-radius: var(--border-radius);
    width: 2.5rem;
    height: 2.5rem;
  }

  .search-btn:hover,
  .refresh-btn:hover {
    background: var(--primary-hover);
  }

  .search-btn:disabled,
  .refresh-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Cards */
  .loading-card,
  .empty-card,
  .sales-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
  }

  .loading-card,
  .empty-card {
    padding: 3rem;
  }

  .loading-content,
  .empty-card {
    text-align: center;
    color: var(--text-muted);
  }

  .loading-content i,
  .empty-card i {
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  /* Stats Header */
  .stats-header {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: #f8f9fa;
  }

  .stat {
    text-align: center;
  }

  .stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-top: 0.25rem;
  }

  /* Table */
  .table-wrapper {
    overflow-x: auto;
  }

  .sales-table {
    width: 100%;
    border-collapse: collapse;
  }

  .sales-table th,
  .sales-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  .sales-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .sales-table tbody tr:hover {
    background: #f8f9fa;
  }

  .sort-btn {
    background: none;
    border: none;
    color: inherit;
    font: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
  }

  .sort-btn:hover {
    color: var(--primary-color);
  }

  .receipt-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
  }

  .receipt-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
  }

  .date-info {
    line-height: 1.3;
  }

  .date {
    font-weight: 500;
  }

  .time {
    font-size: 0.8rem;
    color: var(--text-muted);
  }

  .amount {
    font-weight: 600;
    text-align: right;
  }

  .payment-tag,
  .employee-tag {
    background: #e9ecef;
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .employee-tag {
    background: #e3f2fd;
    color: #1976d2;
  }

  /* Pagination */
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
  }

  .page-numbers {
    display: flex;
    gap: 0.25rem;
  }

  .page-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .page-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .page-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
  }

  .page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .page-ellipsis {
    padding: 0.5rem;
    color: var(--text-muted);
  }

  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
  }

  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--border-radius);
  }

  .modal-close:hover {
    background: #f8f9fa;
    color: var(--text-color);
  }

  .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
  }

  .modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
  }

  .btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
  }

  .btn-secondary:hover {
    background: #5a6268;
  }

  /* Detail Sections */
  .detail-section {
    margin-bottom: 1.5rem;
  }

  .section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .detail-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .detail-value {
    font-weight: 600;
    color: var(--text-color);
  }

  .detail-value.primary {
    color: var(--primary-color);
    font-size: 1.1rem;
  }

  .detail-value.success {
    color: var(--success-color);
    font-size: 1.1rem;
  }

  /* Products Table */
  .products-table {
    overflow-x: auto;
  }

  .products-table table {
    width: 100%;
    border-collapse: collapse;
  }

  .products-table th,
  .products-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
  }

  .products-table th {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .product-info {
    line-height: 1.4;
  }

  .product-name {
    font-weight: 600;
  }

  .product-detail {
    font-size: 0.8rem;
    color: var(--text-muted);
  }

  .quantity-tag {
    background: #e9ecef;
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
  }

  .quantity-tag.weight {
    background: #fff3cd;
    color: #856404;
  }

  .products-table .amount {
    text-align: right;
    font-weight: 500;
  }

  .products-table .total {
    font-weight: 600;
  }

  /* Payment Info */
  .payment-info {
    display: flex;
    justify-content: space-between;
    align-items: end;
    gap: 2rem;
  }

  .payment-methods {
    flex: 1;
  }

  .payment-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .method-name {
    font-weight: 500;
  }

  .method-amount {
    font-weight: 600;
  }

  .payment-total {
    background: #d1edff;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    min-width: 150px;
  }

  .total-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  .total-amount {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--success-color);
    margin-top: 0.25rem;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .filter-row {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-group {
      width: 100%;
    }

    .stats-header {
      flex-direction: column;
      gap: 1rem;
    }

    .payment-info {
      flex-direction: column;
      align-items: stretch;
    }

    .modal-content {
      margin: 0.5rem;
      max-height: 95vh;
    }

    .detail-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
