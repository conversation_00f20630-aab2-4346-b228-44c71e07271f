<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { getTodaysSales } from '../utils/database.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  // Sales state
  let todaysSales = []
  let isLoadingSales = false
  let lastRefresh = null

  onMount(async () => {
    await loadTodaysSales()
  })

  async function loadTodaysSales() {
    isLoadingSales = true
    try {
      todaysSales = await getTodaysSales()
      lastRefresh = new Date()
      console.log('📊 Bugünkü satışlar yüklendi:', todaysSales)
      showSuccess(`${todaysSales.length} satış kaydı yüklendi`)
    } catch (error) {
      console.error('❌ Satışlar yüklenirken hata:', error)
      showError('Satışlar yüklenirken hata oluştu')
    } finally {
      isLoadingSales = false
    }
  }

  function formatTime(dateString) {
    const date = new Date(dateString)
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  function formatCurrency(amount) {
    return amount.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    })
  }

  function formatDate(date) {
    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
</script>

<div class="sales-page">
  <div class="hero light-theme-hero">
    <div class="hero-body">
      <div class="container">
        <h1 class="title light-theme-title">
          <span class="icon">
            <i class="fas fa-chart-line"></i>
          </span>
          Satışlar
        </h1>
        <h2 class="subtitle light-theme-subtitle">
          Bugünkü satış işlemlerini görüntüleyin ve yönetin
        </h2>
      </div>
    </div>
  </div>

  <div class="section">
    <div class="container">
      <!-- Sales Controls -->
      <div class="box light-theme-box mb-5">
        <div class="level">
          <div class="level-left">
            <div class="level-item">
              <h3 class="title is-4 light-theme-text">
                <span class="icon">
                  <i class="fas fa-list"></i>
                </span>
                Bugünkü Satışlar
              </h3>
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <button
                class="button light-theme-button"
                on:click={loadTodaysSales}
                disabled={isLoadingSales}
              >
                <span class="icon">
                  <i class="fas fa-sync-alt"></i>
                </span>
                <span>Yenile</span>
              </button>
            </div>
            {#if lastRefresh}
              <div class="level-item">
                <small class="has-text-grey">
                  Son güncelleme: {formatDate(lastRefresh)}
                </small>
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Sales Content -->
      {#if isLoadingSales}
        <div class="box light-theme-box">
          <div class="has-text-centered">
            <span class="icon is-large">
              <i class="fas fa-spinner fa-pulse fa-2x"></i>
            </span>
            <p class="mt-3">Satışlar yükleniyor...</p>
          </div>
        </div>
      {:else if todaysSales.length === 0}
        <div class="box light-theme-box">
          <div class="notification is-info is-light">
            <span class="icon">
              <i class="fas fa-info-circle"></i>
            </span>
            <span>Bugün henüz satış yapılmamış.</span>
          </div>
        </div>
      {:else}
        <!-- Sales Table -->
        <div class="box light-theme-box">
          <div class="table-container">
            <table class="table is-fullwidth is-striped is-hoverable">
              <thead>
                <tr>
                  <th>Fiş No</th>
                  <th>Saat</th>
                  <th class="has-text-right">Tutar</th>
                  <th>Ödeme</th>
                  <th>Personel</th>
                </tr>
              </thead>
              <tbody>
                {#each todaysSales as sale (sale.id)}
                  <tr>
                    <td>
                      <span class="tag is-primary">{sale.receipt_number}</span>
                    </td>
                    <td>{formatTime(sale.created_at)}</td>
                    <td class="has-text-right">
                      <strong>{formatCurrency(sale.total_price)}</strong>
                    </td>
                    <td>
                      <span class="tag is-light">{sale.payment_methods || 'Bilinmiyor'}</span>
                    </td>
                    <td>
                      <span class="tag is-info is-light">
                        {sale.employee_full_name || sale.employee_code || 'Bilinmiyor'}
                      </span>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Sales Summary -->
        <div class="box light-theme-box">
          <h4 class="title is-5 light-theme-text">
            <span class="icon">
              <i class="fas fa-chart-bar"></i>
            </span>
            Günlük Özet
          </h4>
          <div class="level">
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Satış</p>
                <p class="title is-4">{todaysSales.length}</p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Toplam Tutar</p>
                <p class="title is-4">
                  {formatCurrency(todaysSales.reduce((sum, sale) => sum + sale.total_price, 0))}
                </p>
              </div>
            </div>
            <div class="level-item has-text-centered">
              <div>
                <p class="heading">Ortalama Satış</p>
                <p class="title is-4">
                  {formatCurrency(
                    todaysSales.reduce((sum, sale) => sum + sale.total_price, 0) /
                      todaysSales.length
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  /* Light Theme Styles */
  .light-theme-hero {
    background: linear-gradient(
      135deg,
      var(--color-light-secondary),
      var(--color-light-secondary-gradient)
    );
    color: white;
  }

  .light-theme-title {
    color: white !important;
  }

  .light-theme-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .light-theme-box {
    background-color: var(--color-light) !important;
    border: 1px solid var(--color-light-trd);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .light-theme-text {
    color: var(--color-light-text) !important;
  }

  /* Button Styles */
  .light-theme-button {
    background-color: var(--color-theme-light) !important;
    border-color: var(--color-theme-light) !important;
    color: white !important;
  }

  .light-theme-button:hover {
    background-color: #c91653 !important;
    border-color: #c91653 !important;
  }
</style>
