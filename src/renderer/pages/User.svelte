<script>
  import { onMount } from 'svelte'
  import { push } from 'svelte-spa-router'
  import { authStore } from '../stores/authStore.js'
  import { showError, showSuccess } from '../utils/toastUtils.js'

  // Authentication state
  $: authState = $authStore

  // Check authentication and redirect if not authenticated
  $: if (authState && !authState.isLoading && !authState.isAuthenticated) {
    push('/login')
  }

  let users = []
  let loading = false
  let showAddForm = false
  let newUser = { name: '', email: '', age: '' }
  let editingUser = null

  onMount(() => {
    // Load users asynchronously without blocking navigation
    loadUsers().catch(error => {
      console.error('Error loading users on mount:', error)
    })
  })

  async function loadUsers() {
    try {
      loading = true
      const { getAllUsers } = await import('../utils/database.js')
      users = getAllUsers()
      showSuccess('Users loaded successfully!')
    } catch (error) {
      console.error('Error loading users:', error)
      showError('Failed to load users')
    } finally {
      loading = false
    }
  }

  async function addUser() {
    if (!newUser.name || !newUser.email) {
      showError('Name and email are required')
      return
    }

    try {
      const userData = {
        name: newUser.name,
        email: newUser.email,
        age: parseInt(newUser.age) || null,
      }

      const { addUser: dbAddUser } = await import('../utils/database.js')
      dbAddUser(userData)
      showSuccess('User added successfully!')

      // Reset form and reload users
      newUser = { name: '', email: '', age: '' }
      showAddForm = false
      await loadUsers()
    } catch (error) {
      console.error('Error adding user:', error)
      showError('Failed to add user')
    }
  }

  async function updateUser() {
    if (!editingUser.name || !editingUser.email) {
      showError('Name and email are required')
      return
    }

    try {
      const userData = {
        name: editingUser.name,
        email: editingUser.email,
        age: parseInt(editingUser.age) || null,
      }

      const { updateUser: dbUpdateUser } = await import('../utils/database.js')
      dbUpdateUser(editingUser.id, userData)
      showSuccess('User updated successfully!')

      // Reset editing state and reload users
      editingUser = null
      await loadUsers()
    } catch (error) {
      console.error('Error updating user:', error)
      showError('Failed to update user')
    }
  }

  async function deleteUser(id) {
    if (!confirm('Are you sure you want to delete this user?')) {
      return
    }

    try {
      const { deleteUser: dbDeleteUser } = await import('../utils/database.js')
      dbDeleteUser(id)
      showSuccess('User deleted successfully!')
      await loadUsers()
    } catch (error) {
      console.error('Error deleting user:', error)
      showError('Failed to delete user')
    }
  }

  function startEdit(user) {
    editingUser = { ...user }
  }

  function cancelEdit() {
    editingUser = null
  }
</script>

<div class="home-page">
  <div class="hero light-theme-hero">
    <div class="hero-body">
      <div class="container">
        <h1 class="title light-theme-title">
          <span class="icon">
            <i class="fas fa-home"></i>
          </span>
          Welcome to SevlteFurpa
        </h1>
        <h2 class="subtitle light-theme-subtitle">
          Electron.js desktop application with Svelte, Vite, and Bulma
        </h2>
      </div>
    </div>
  </div>

  <div class="section">
    <div class="container">
      <div class="columns">
        <div class="column">
          <div class="box light-theme-box">
            <h3 class="title is-4 light-theme-text">
              <span class="icon">
                <i class="fas fa-users"></i>
              </span>
              User Management
            </h3>

            <div class="field">
              <div class="control">
                <button
                  class="button light-theme-button"
                  on:click={() => (showAddForm = !showAddForm)}
                >
                  <span class="icon">
                    <i class="fas fa-plus"></i>
                  </span>
                  <span>Add New User</span>
                </button>

                <button
                  class="button light-theme-button-secondary ml-2"
                  on:click={loadUsers}
                  class:is-loading={loading}
                >
                  <span class="icon">
                    <i class="fas fa-refresh"></i>
                  </span>
                  <span>Refresh</span>
                </button>
              </div>
            </div>

            {#if showAddForm}
              <div class="box light-theme-form-box">
                <h4 class="title is-5 light-theme-text">Add New User</h4>

                <div class="field">
                  <label class="label light-theme-text" for="user-name">Name</label>
                  <div class="control">
                    <input
                      id="user-name"
                      class="input light-theme-input"
                      type="text"
                      bind:value={newUser.name}
                      placeholder="Enter name"
                    />
                  </div>
                </div>

                <div class="field">
                  <label class="label light-theme-text" for="user-email">Email</label>
                  <div class="control">
                    <input
                      id="user-email"
                      class="input light-theme-input"
                      type="email"
                      bind:value={newUser.email}
                      placeholder="Enter email"
                    />
                  </div>
                </div>

                <div class="field">
                  <label class="label light-theme-text" for="user-age">Age</label>
                  <div class="control">
                    <input
                      id="user-age"
                      class="input light-theme-input"
                      type="number"
                      bind:value={newUser.age}
                      placeholder="Enter age"
                    />
                  </div>
                </div>

                <div class="field is-grouped">
                  <div class="control">
                    <button class="button light-theme-button" on:click={addUser}> Add User </button>
                  </div>
                  <div class="control">
                    <button
                      class="button light-theme-button-light"
                      on:click={() => (showAddForm = false)}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            {/if}

            {#if loading}
              <div class="has-text-centered light-theme-text">
                <div class="loader light-theme-loader"></div>
                <p>Loading users...</p>
              </div>
            {:else if users.length === 0}
              <div class="notification light-theme-notification">
                <p>No users found. Add some users to get started!</p>
              </div>
            {:else}
              <div class="table-container">
                <table class="table is-fullwidth is-striped light-theme-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Age</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each users as user (user.id)}
                      <tr>
                        <td>{user.id}</td>
                        <td>
                          {#if editingUser && editingUser.id === user.id}
                            <input class="input is-small" bind:value={editingUser.name} />
                          {:else}
                            {user.name}
                          {/if}
                        </td>
                        <td>
                          {#if editingUser && editingUser.id === user.id}
                            <input
                              class="input is-small"
                              type="email"
                              bind:value={editingUser.email}
                            />
                          {:else}
                            {user.email}
                          {/if}
                        </td>
                        <td>
                          {#if editingUser && editingUser.id === user.id}
                            <input
                              class="input is-small"
                              type="number"
                              bind:value={editingUser.age}
                            />
                          {:else}
                            {user.age || 'N/A'}
                          {/if}
                        </td>
                        <td>{new Date(user.created_at).toLocaleDateString()}</td>
                        <td>
                          {#if editingUser && editingUser.id === user.id}
                            <button class="button is-success is-small mr-1" on:click={updateUser}>
                              <span class="icon">
                                <i class="fas fa-check"></i>
                              </span>
                            </button>
                            <button class="button is-light is-small" on:click={cancelEdit}>
                              <span class="icon">
                                <i class="fas fa-times"></i>
                              </span>
                            </button>
                          {:else}
                            <button
                              class="button is-info is-small mr-1"
                              on:click={() => startEdit(user)}
                            >
                              <span class="icon">
                                <i class="fas fa-edit"></i>
                              </span>
                            </button>
                            <button
                              class="button is-danger is-small"
                              on:click={() => deleteUser(user.id)}
                            >
                              <span class="icon">
                                <i class="fas fa-trash"></i>
                              </span>
                            </button>
                          {/if}
                        </td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Light Theme Styles */
  .light-theme-hero {
    background: linear-gradient(
      135deg,
      var(--color-light-secondary),
      var(--color-light-secondary-gradient)
    );
    color: white;
  }

  .light-theme-title {
    color: white !important;
  }

  .light-theme-subtitle {
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .light-theme-box {
    background-color: var(--color-light) !important;
    border: 1px solid var(--color-light-trd);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .light-theme-form-box {
    background-color: var(--color-light-secondary) !important;
    border: 1px solid var(--color-light-trd);
    color: white;
  }

  .light-theme-text {
    color: var(--color-light-text) !important;
  }

  .light-theme-form-box .light-theme-text {
    color: white !important;
  }

  .light-theme-button {
    background-color: var(--color-theme-light) !important;
    border-color: var(--color-theme-light) !important;
    color: white !important;
  }

  .light-theme-button:hover {
    background-color: #c91653 !important;
    border-color: #c91653 !important;
  }

  .light-theme-button-secondary {
    background-color: var(--color-light-secondary) !important;
    border-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-button-secondary:hover {
    background-color: var(--color-light-secondary-gradient) !important;
    border-color: var(--color-light-secondary-gradient) !important;
  }

  .light-theme-button-light {
    background-color: var(--color-light-trd) !important;
    border-color: var(--color-light-trd) !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-button-light:hover {
    background-color: #c4c2cf !important;
    border-color: #c4c2cf !important;
  }

  .light-theme-input {
    border-color: var(--color-light-trd) !important;
    background-color: white !important;
    color: var(--color-light-text) !important;
  }

  .light-theme-input:focus {
    border-color: var(--color-theme-light) !important;
    box-shadow: 0 0 0 0.125em rgba(223, 27, 95, 0.25) !important;
  }

  .light-theme-notification {
    background-color: var(--color-light-secondary) !important;
    color: white !important;
  }

  .light-theme-table {
    background-color: var(--color-light) !important;
  }

  .light-theme-table th {
    background-color: var(--color-light-secondary) !important;
    color: white !important;
    border-color: var(--color-light-trd) !important;
  }

  .light-theme-table td {
    color: var(--color-light-text) !important;
    border-color: var(--color-light-trd) !important;
  }

  .light-theme-table tbody tr:nth-child(even) {
    background-color: rgba(234, 230, 238, 0.5) !important;
  }

  .light-theme-loader {
    border: 4px solid var(--color-light-trd);
    border-top: 4px solid var(--color-theme-light);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
