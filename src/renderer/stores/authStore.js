import { push } from 'svelte-spa-router'
import { writable } from 'svelte/store'
import { showError, showSuccess } from '../utils/toastUtils.js'

// Secure IPC access to main process auth service
const electronAPI = window.electronAPI

// Individual reactive stores for better component integration
export const isAuthenticated = writable(false)
export const currentUser = writable(null)
export const isLoading = writable(true)

// Authentication store
function createAuthStore() {
  const { subscribe, set, update } = writable({
    isAuthenticated: false,
    user: null,
    isLoading: true,
  })

  return {
    subscribe,

    // Initialize authentication state
    async init() {
      try {
        update(state => ({ ...state, isLoading: true }))

        const authStatus = electronAPI.auth.isUserAuthenticated()

        if (authStatus) {
          const user = electronAPI.auth.getCurrentUser()
          set({
            isAuthenticated: true,
            user,
            isLoading: false,
          })
          // Update individual stores
          isAuthenticated.set(true)
          currentUser.set(user)
          isLoading.set(false)
        } else {
          set({
            isAuthenticated: false,
            user: null,
            isLoading: false,
          })
          // Update individual stores
          isAuthenticated.set(false)
          currentUser.set(null)
          isLoading.set(false)
        }
      } catch (error) {
        console.error('❌ Error initializing auth state:', error)
        set({
          isAuthenticated: false,
          user: null,
          isLoading: false,
        })
      }
    },

    // Login function
    async login(credentials) {
      try {
        console.log('🔐 AuthStore: Login attempt with credentials:', {
          username: credentials.username,
          rememberMe: credentials.rememberMe,
        })
        update(state => ({ ...state, isLoading: true }))

        // Check if electronAPI is available
        if (!electronAPI || !electronAPI.auth) {
          throw new Error('Electron API not available')
        }

        const result = await electronAPI.auth.login(credentials)
        console.log('🔐 AuthStore: Login result:', result)

        if (result.success) {
          // Use user data from login result instead of separate call
          const user = result.user
          console.log('🔐 AuthStore: Login result user:', user)
          set({
            isAuthenticated: true,
            user,
            isLoading: false,
          })
          // Update individual stores
          isAuthenticated.set(true)
          currentUser.set(user)
          isLoading.set(false)

          console.log('🔐 AuthStore: Individual stores updated')
          console.log('🔐 AuthStore: currentUser set to:', user)

          showSuccess('Giriş başarılı! Hoş geldiniz.')

          // Redirect to home page after a short delay
          setTimeout(() => push('/home'), 1000)
          return { success: true, user }
        } else {
          set({
            isAuthenticated: false,
            user: null,
            isLoading: false,
          })
          // Update individual stores
          isAuthenticated.set(false)
          currentUser.set(null)
          isLoading.set(false)

          showError(
            result.message || 'Giriş başarısız. Lütfen kullanıcı bilgilerinizi kontrol edin.'
          )
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('❌ AuthStore: Login error:', error)
        set({
          isAuthenticated: false,
          user: null,
          isLoading: false,
        })
        showError('Giriş sırasında bir hata oluştu. Lütfen tekrar deneyin.')
        return { success: false, message: `Giriş sırasında bir hata oluştu: ${error.message}` }
      }
    },

    // Logout function
    async logout() {
      try {
        const result = await electronAPI.auth.logout()

        if (result.success) {
          set({
            isAuthenticated: false,
            user: null,
            isLoading: false,
          })
          // Update individual stores
          isAuthenticated.set(false)
          currentUser.set(null)
          isLoading.set(false)

          showSuccess('Çıkış başarılı. Görüşmek üzere!')

          // Redirect to login page
          setTimeout(() => push('/'), 500)
        } else {
          showError(result.message || 'Çıkış başarısız')
        }
      } catch (error) {
        console.error('Logout error:', error)
        showError('Çıkış sırasında bir hata oluştu')
      }
    },

    // Check if user is authenticated (for route guards)
    async checkAuth() {
      try {
        const authStatus = electronAPI.auth.isUserAuthenticated()
        return authStatus
      } catch (error) {
        console.error('Error checking auth status:', error)
        return false
      }
    },

    // Force update authentication state
    async refresh() {
      await this.init()
    },

    // Helper functions for individual store access
    setUser: userData => {
      currentUser.set(userData)
      isAuthenticated.set(true)
      console.log('🔐 User data set in store:', userData)
    },

    clearUser: () => {
      currentUser.set(null)
      isAuthenticated.set(false)
      console.log('🔐 User data cleared from store')
    },

    getCurrentUser: () => {
      let user = null
      currentUser.subscribe(value => {
        user = value
      })()
      return user
    },

    isUserAuthenticated: () => {
      let authenticated = false
      isAuthenticated.subscribe(value => {
        authenticated = value
      })()
      return authenticated
    },
  }
}

export const authStore = createAuthStore()
