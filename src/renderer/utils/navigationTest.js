// Navigation test utility to verify routing works after toast notifications
import { getCurrentRoute, navigateTo } from './navigationUtils.js'
import { showSuccess } from './toastUtils.js'

export class NavigationTest {
  constructor() {
    this.testResults = []
    this.isRunning = false
  }

  addResult(message, type = 'info') {
    this.testResults.push({
      id: Date.now(),
      message,
      type,
      timestamp: new Date().toISOString(),
    })
    console.log(`[NavigationTest] ${type.toUpperCase()}: ${message}`)
  }

  async runComprehensiveTest() {
    if (this.isRunning) {
      this.addResult('Test already running', 'warning')
      return this.testResults
    }

    this.isRunning = true
    this.testResults = []

    this.addResult('Starting simplified navigation test...', 'info')

    try {
      // Test 1: Basic navigation without toasts
      await this.testBasicNavigation()

      // Test 2: Navigation with single toast (simplified)
      await this.testNavigationWithSingleToast()

      this.addResult('All navigation tests completed successfully!', 'success')
    } catch (error) {
      this.addResult(`Test failed: ${error.message}`, 'error')
    } finally {
      this.isRunning = false
    }

    return this.testResults
  }

  async testBasicNavigation() {
    this.addResult('Testing basic navigation...', 'info')

    // Navigate to home
    navigateTo('#/')
    await this.wait(100) // Reduced wait time

    if (getCurrentRoute() === '/') {
      this.addResult('✓ Navigation to home successful', 'success')
    } else {
      throw new Error('Failed to navigate to home')
    }

    // Navigate to test
    navigateTo('#/test')
    await this.wait(100) // Reduced wait time

    if (getCurrentRoute() === '/test') {
      this.addResult('✓ Navigation to test page successful', 'success')
    } else {
      throw new Error('Failed to navigate to test page')
    }

    // Navigate back to home
    navigateTo('#/')
    await this.wait(100) // Reduced wait time

    if (getCurrentRoute() === '/') {
      this.addResult('✓ Navigation back to home successful', 'success')
    } else {
      throw new Error('Failed to navigate back to home')
    }
  }

  async testNavigationWithSingleToast() {
    this.addResult('Testing navigation with toast...', 'info')

    // Show success toast and navigate
    showSuccess('Test navigation message')
    await this.wait(50) // Minimal wait

    navigateTo('#/test')
    await this.wait(150) // Reduced wait time

    if (getCurrentRoute() === '/test') {
      this.addResult('✓ Navigation works with toast', 'success')
    } else {
      throw new Error('Navigation failed with toast')
    }

    // Navigate back
    navigateTo('#/')
    await this.wait(150) // Reduced wait time

    if (getCurrentRoute() === '/') {
      this.addResult('✓ Return navigation successful', 'success')
    } else {
      throw new Error('Return navigation failed')
    }
  }

  // Removed complex test methods for better performance

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  getResults() {
    return this.testResults
  }

  isTestRunning() {
    return this.isRunning
  }
}

// Export singleton instance
export const navigationTest = new NavigationTest()

export default navigationTest
