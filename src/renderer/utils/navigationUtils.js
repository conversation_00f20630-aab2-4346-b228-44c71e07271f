// Navigation utility to handle routing conflicts
class NavigationManager {
  constructor() {
    this.isNavigating = false
    this.pendingNavigation = null
    this.setupEventListeners()
  }

  setupEventListeners() {
    if (typeof window !== 'undefined') {
      // Listen for hash changes
      window.addEventListener('hashchange', this.handleHashChange.bind(this))

      // Listen for popstate events
      window.addEventListener('popstate', this.handlePopState.bind(this))

      // Prevent navigation during toast animations
      window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this))
    }
  }

  handleHashChange(_event) {
    console.log('Hash changed:', window.location.hash)
    this.isNavigating = false
  }

  handlePopState(_event) {
    console.log('Pop state:', _event.state)
    this.isNavigating = false
  }

  handleBeforeUnload(_event) {
    // Clear any pending navigation
    this.pendingNavigation = null
    this.isNavigating = false
  }

  // Safe navigation method - simplified for better performance
  navigateTo(path) {
    if (this.isNavigating) {
      // Queue the navigation instead of blocking
      this.pendingNavigation = path
      return
    }

    this.isNavigating = true

    try {
      // Ensure path starts with #
      const hashPath = path.startsWith('#') ? path : `#${path}`

      // Use requestAnimationFrame for smooth navigation
      if (window.requestAnimationFrame) {
        window.requestAnimationFrame(() => {
          window.location.hash = hashPath
          // Reset navigation state quickly
          setTimeout(() => {
            this.isNavigating = false
            // Process any pending navigation
            if (this.pendingNavigation) {
              const pending = this.pendingNavigation
              this.pendingNavigation = null
              this.navigateTo(pending)
            }
          }, 50) // Reduced delay for responsiveness
        })
      } else {
        // Fallback for older browsers
        window.location.hash = hashPath
        setTimeout(() => {
          this.isNavigating = false
          if (this.pendingNavigation) {
            const pending = this.pendingNavigation
            this.pendingNavigation = null
            this.navigateTo(pending)
          }
        }, 50)
      }
    } catch (error) {
      console.error('Navigation error:', error)
      this.isNavigating = false
    }
  }

  // Get current route
  getCurrentRoute() {
    if (typeof window === 'undefined') return '/'

    const hash = window.location.hash
    return hash.startsWith('#') ? hash.substring(1) : hash || '/'
  }

  // Check if currently navigating
  isCurrentlyNavigating() {
    return this.isNavigating
  }

  // Force reset navigation state
  resetNavigationState() {
    this.isNavigating = false
    this.pendingNavigation = null
  }
}

// Create singleton instance
export const navigationManager = new NavigationManager()

// Export convenience functions
export const navigateTo = path => navigationManager.navigateTo(path)
export const getCurrentRoute = () => navigationManager.getCurrentRoute()
export const isNavigating = () => navigationManager.isCurrentlyNavigating()
export const resetNavigation = () => navigationManager.resetNavigationState()

export default navigationManager
