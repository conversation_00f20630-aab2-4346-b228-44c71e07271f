import toast from 'svelte-french-toast'

// Global AudioContext management to prevent memory leaks and browser limits
class AudioManager {
  constructor() {
    this.audioContext = null
    this.isInitialized = false
    this.pendingSounds = []
    this.isPlaying = false
  }

  // Initialize AudioContext only once
  async initializeAudioContext() {
    if (this.isInitialized && this.audioContext && this.audioContext.state !== 'closed') {
      return this.audioContext
    }

    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()

      // Resume context if suspended (required by some browsers)
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      this.isInitialized = true
      console.log('🔊 AudioContext başlatıldı:', this.audioContext.state)
      return this.audioContext
    } catch (error) {
      console.error('❌ AudioContext başlatılamadı:', error)
      this.isInitialized = false
      throw error
    }
  }

  // Play beep sound with proper resource management
  async playBeepSound() {
    // Prevent overlapping sounds to avoid resource exhaustion
    if (this.isPlaying) {
      console.log('🔊 Se<PERSON> ç<PERSON>, yeni ses atlandı')
      return
    }

    try {
      this.isPlaying = true
      const audioContext = await this.initializeAudioContext()

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      // Connect oscillator to gain to speakers
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // Configure beep sound
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime) // 800Hz frequency
      oscillator.type = 'sine' // Sine wave for clean beep

      // Configure volume (fade in/out for smooth sound)
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01) // Fade in
      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.15) // Fade out

      // Play beep for 150ms
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.15)

      // Clean up after sound finishes
      oscillator.onended = () => {
        oscillator.disconnect()
        gainNode.disconnect()
        this.isPlaying = false
        console.log('🔊 Beep sesi tamamlandı ve temizlendi')
      }

      console.log('🔊 Beep sesi çalındı')
    } catch (error) {
      this.isPlaying = false
      console.error('❌ Beep sesi çalınamadı:', error)
      this.playFallbackBeep()
    }
  }

  // Play error sound with proper resource management
  async playErrorSound() {
    // Prevent overlapping sounds to avoid resource exhaustion
    if (this.isPlaying) {
      console.log('🔊 Ses çalınıyor, yeni ses atlandı')
      return
    }

    try {
      this.isPlaying = true
      const audioContext = await this.initializeAudioContext()

      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      // Connect oscillator to gain to speakers
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // Configure error sound (lower frequency, longer duration)
      oscillator.frequency.setValueAtTime(300, audioContext.currentTime) // 300Hz frequency (lower/deeper)
      oscillator.type = 'sawtooth' // Sawtooth wave for harsher error sound

      // Configure volume (fade in/out for smooth sound)
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.02) // Fade in
      gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3) // Fade out

      // Play error sound for 300ms (longer than beep)
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.3)

      // Clean up after sound finishes
      oscillator.onended = () => {
        oscillator.disconnect()
        gainNode.disconnect()
        this.isPlaying = false
        console.log('🔊 Hata sesi tamamlandı ve temizlendi')
      }

      console.log('🔊 Hata sesi çalındı')
    } catch (error) {
      this.isPlaying = false
      console.error('❌ Hata sesi çalınamadı:', error)
      this.playFallbackError()
    }
  }

  // Fallback beep using HTML5 Audio
  playFallbackBeep() {
    try {
      const audio = new Audio(
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'
      )
      audio.volume = 0.3
      audio.play()
      console.log('🔊 Fallback beep çalındı')
    } catch (fallbackError) {
      console.error('❌ Fallback beep de çalınamadı:', fallbackError)
    }
  }

  // Fallback error sound using HTML5 Audio
  playFallbackError() {
    try {
      const audio = new Audio(
        'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'
      )
      audio.volume = 0.2
      audio.play()
      console.log('🔊 Fallback hata sesi çalındı')
    } catch (fallbackError) {
      console.error('❌ Fallback hata sesi de çalınamadı:', fallbackError)
    }
  }

  // Clean up resources
  cleanup() {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      console.log('🔊 AudioContext kapatıldı')
    }
    this.isInitialized = false
    this.isPlaying = false
  }
}

// Create singleton AudioManager instance
const audioManager = new AudioManager()

// Export sound functions that use the managed AudioContext
function playBeepSound() {
  audioManager.playBeepSound()
}

function playErrorSound() {
  audioManager.playErrorSound()
}

// Toast utility to prevent conflicts with routing
class ToastManager {
  constructor() {
    this.toastQueue = []
    this.isProcessing = false
  }

  // Queue toast to prevent conflicts with navigation
  queueToast(type, message, options = {}) {
    this.toastQueue.push({ type, message, options })
    this.processQueue()
  }

  async processQueue() {
    if (this.isProcessing || this.toastQueue.length === 0) {
      return
    }

    this.isProcessing = true

    while (this.toastQueue.length > 0) {
      const { type, message, options } = this.toastQueue.shift()

      // Add small delay to prevent conflicts
      await new Promise(resolve => setTimeout(resolve, 50))

      try {
        switch (type) {
          case 'success':
            // Play beep sound for success
            playBeepSound()
            toast.success(message, {
              duration: 2000,
              ...options,
            })
            break
          case 'error':
            // Play error sound for error
            playErrorSound()
            toast.error(message, {
              duration: 4000,
              ...options,
            })
            break
          case 'info':
            toast(message, {
              icon: 'ℹ️',
              duration: 3000,
              ...options,
            })
            break
          default:
            toast(message, options)
        }
      } catch (error) {
        console.warn('Toast error:', error)
      }

      // Small delay between toasts
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    this.isProcessing = false
  }

  // Convenience methods
  success(message, options = {}) {
    this.queueToast('success', message, options)
  }

  error(message, options = {}) {
    this.queueToast('error', message, options)
  }

  info(message, options = {}) {
    this.queueToast('info', message, options)
  }

  // Clear all pending toasts
  clear() {
    this.toastQueue = []
    toast.dismiss()
  }
}

// Create singleton instance
export const toastManager = new ToastManager()

// Export convenience functions
export const showSuccess = (message, options) => toastManager.success(message, options)
export const showError = (message, options) => toastManager.error(message, options)
export const showInfo = (message, options) => toastManager.info(message, options)
export const clearToasts = () => toastManager.clear()

// Export sound functions
export { playBeepSound, playErrorSound }

// Export AudioManager for cleanup if needed
export { audioManager }

export default toastManager
